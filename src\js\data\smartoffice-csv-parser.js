/**
 * @file SmartOffice CSV解析器
 * @description 纯JavaScript实现的CSV文件解析器，支持引号、转义字符和大文件处理
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function CSVParser
 * @description CSV解析器构造函数
 * @constructor
 * @param {Object} options - 解析选项
 * @param {string} options.delimiter - 字段分隔符（默认逗号）
 * @param {string} options.quote - 引号字符（默认双引号）
 * @param {string} options.escape - 转义字符（默认双引号）
 * @param {boolean} options.hasHeader - 是否包含标题行（默认true）
 * @param {string} options.encoding - 文件编码（默认UTF-8）
 * @param {number} options.chunkSize - 分块处理大小（默认1MB）
 */
function CSVParser(options) {
    // 默认配置
    this.options = SmartOffice.Utils.Helpers.extend({
        delimiter: ',',           // 字段分隔符
        quote: '"',              // 引号字符
        escape: '"',             // 转义字符
        hasHeader: true,         // 是否包含标题行
        encoding: 'UTF-8',       // 文件编码
        chunkSize: 1024 * 1024,  // 1MB分块大小
        skipEmptyLines: true,    // 跳过空行
        trimFields: true         // 去除字段首尾空格
    }, options || {});

    // 解析状态
    this.isParsingInProgress = false;
    this.currentProgress = 0;
    this.totalBytes = 0;
    this.processedBytes = 0;

    // 解析结果
    this.headers = [];
    this.data = [];
    this.errors = [];
    this.statistics = {
        totalRows: 0,
        totalColumns: 0,
        emptyRows: 0,
        errorRows: 0,
        parseTime: 0
    };

    // 依赖注入
    this.helpers = SmartOffice.Utils.Helpers;
    this.dataValidator = new SmartOffice.Data.DataValidator();

    SmartOffice.log('info', 'CSVParser初始化完成');
}

/**
 * @function CSVParser.prototype.parse
 * @description 解析CSV内容
 * @param {string} content - CSV文件内容
 * @param {Object} callbacks - 回调函数
 * @param {Function} callbacks.onProgress - 进度回调 (progress: 0-1)
 * @param {Function} callbacks.onComplete - 完成回调 (result)
 * @param {Function} callbacks.onError - 错误回调 (error)
 * @returns {Promise} 解析Promise
 */
CSVParser.prototype.parse = function(content, callbacks) {
    const self = this;
    const startTime = Date.now();

    // 重置状态
    this.resetState();
    this.isParsingInProgress = true;
    this.totalBytes = content.length;

    // 默认回调
    callbacks = callbacks || {};
    const onProgress = callbacks.onProgress || function() {};
    const onComplete = callbacks.onComplete || function() {};
    const onError = callbacks.onError || function() {};

    try {
        // 验证输入
        if (!content || typeof content !== 'string') {
            throw new Error('无效的CSV内容');
        }

        if (content.length === 0) {
            throw new Error('CSV文件为空');
        }

        SmartOffice.log('info', '开始解析CSV，大小:', this.formatBytes(content.length));

        // 使用setTimeout实现异步处理，避免阻塞UI
        setTimeout(function() {
            try {
                self.parseContent(content, onProgress, function(result) {
                    self.statistics.parseTime = Date.now() - startTime;
                    self.isParsingInProgress = false;

                    SmartOffice.log('info', 'CSV解析完成，耗时:', self.statistics.parseTime + 'ms');
                    onComplete(result);
                }, onError);
            } catch (error) {
                self.isParsingInProgress = false;
                SmartOffice.log('error', 'CSV解析失败:', error);
                onError(error);
            }
        }, 10);

    } catch (error) {
        this.isParsingInProgress = false;
        SmartOffice.log('error', 'CSV解析初始化失败:', error);
        onError(error);
    }
};

/**
 * @function CSVParser.prototype.parseContent
 * @description 解析CSV内容的核心方法
 * @param {string} content - CSV内容
 * @param {Function} onProgress - 进度回调
 * @param {Function} onComplete - 完成回调
 * @param {Function} onError - 错误回调
 */
CSVParser.prototype.parseContent = function(content, onProgress, onComplete, onError) {
    try {
        // 分行处理
        const lines = this.splitLines(content);
        const totalLines = lines.length;

        if (totalLines === 0) {
            throw new Error('CSV文件没有有效内容');
        }

        // 解析标题行
        let dataStartIndex = 0;
        if (this.options.hasHeader && totalLines > 0) {
            this.headers = this.parseLine(lines[0], 0);
            dataStartIndex = 1;

            if (this.headers.length === 0) {
                throw new Error('无法解析CSV标题行');
            }
        }

        // 解析数据行
        this.data = [];
        this.errors = [];

        for (let i = dataStartIndex; i < totalLines; i++) {
            try {
                const line = lines[i];

                // 跳过空行
                if (this.options.skipEmptyLines && this.isEmptyLine(line)) {
                    this.statistics.emptyRows++;
                    continue;
                }

                // 解析行数据
                const rowData = this.parseLine(line, i);

                if (rowData.length > 0) {
                    // 如果有标题行，将数据转换为对象
                    if (this.options.hasHeader && this.headers.length > 0) {
                        const rowObject = {};
                        for (let j = 0; j < this.headers.length; j++) {
                            rowObject[this.headers[j]] = rowData[j] || '';
                        }
                        this.data.push(rowObject);
                    } else {
                        this.data.push(rowData);
                    }
                }

                // 更新进度
                if (i % 100 === 0) { // 每100行更新一次进度
                    this.currentProgress = i / totalLines;
                    onProgress(this.currentProgress);
                }

            } catch (rowError) {
                this.errors.push({
                    line: i + 1,
                    content: lines[i],
                    error: rowError.message
                });
                this.statistics.errorRows++;
            }
        }

        // 更新统计信息
        this.statistics.totalRows = this.data.length;
        this.statistics.totalColumns = this.headers.length || (this.data.length > 0 ? this.data[0].length : 0);

        // 最终进度更新
        this.currentProgress = 1;
        onProgress(1);

        // 构建结果对象
        const result = {
            headers: this.headers,
            data: this.data,
            errors: this.errors,
            statistics: this.statistics,
            options: this.options
        };

        // 验证解析结果
        const validationResult = this.validateResult(result);
        if (!validationResult.isValid) {
            throw new Error('解析结果验证失败: ' + validationResult.message);
        }

        onComplete(result);

    } catch (error) {
        SmartOffice.log('error', 'CSV内容解析失败:', error);
        onError(error);
    }
};

/**
 * @function CSVParser.prototype.splitLines
 * @description 将CSV内容分割为行数组
 * @param {string} content - CSV内容
 * @returns {Array} 行数组
 */
CSVParser.prototype.splitLines = function(content) {
    // 处理不同的换行符：\r\n, \n, \r
    const lines = content.split(/\r\n|\n|\r/);

    // 过滤掉最后的空行（如果存在）
    if (lines.length > 0 && lines[lines.length - 1] === '') {
        lines.pop();
    }

    return lines;
};

/**
 * @function CSVParser.prototype.parseLine
 * @description 解析单行CSV数据
 * @param {string} line - 行内容
 * @param {number} lineNumber - 行号（用于错误报告）
 * @returns {Array} 字段数组
 */
CSVParser.prototype.parseLine = function(line, lineNumber) {
    if (!line || typeof line !== 'string') {
        return [];
    }

    const fields = [];
    let currentField = '';
    let inQuotes = false;
    let i = 0;

    while (i < line.length) {
        const char = line[i];
        const nextChar = i + 1 < line.length ? line[i + 1] : null;

        if (char === this.options.quote) {
            if (inQuotes) {
                // 在引号内
                if (nextChar === this.options.quote) {
                    // 转义的引号
                    currentField += this.options.quote;
                    i += 2; // 跳过两个字符
                    continue;
                } else {
                    // 引号结束
                    inQuotes = false;
                }
            } else {
                // 引号开始
                inQuotes = true;
            }
        } else if (char === this.options.delimiter && !inQuotes) {
            // 字段分隔符（不在引号内）
            fields.push(this.processField(currentField));
            currentField = '';
        } else {
            // 普通字符
            currentField += char;
        }

        i++;
    }

    // 添加最后一个字段
    fields.push(this.processField(currentField));

    // 检查是否有未闭合的引号
    if (inQuotes) {
        SmartOffice.log('warn', `第${lineNumber + 1}行存在未闭合的引号`);
    }

    return fields;
};

/**
 * @function CSVParser.prototype.processField
 * @description 处理字段值（去除空格、处理特殊字符等）
 * @param {string} field - 原始字段值
 * @returns {string} 处理后的字段值
 */
CSVParser.prototype.processField = function(field) {
    if (typeof field !== 'string') {
        return '';
    }

    // 去除首尾空格（如果配置要求）
    if (this.options.trimFields) {
        field = field.trim();
    }

    return field;
};

/**
 * @function CSVParser.prototype.isEmptyLine
 * @description 检查是否为空行
 * @param {string} line - 行内容
 * @returns {boolean} 是否为空行
 */
CSVParser.prototype.isEmptyLine = function(line) {
    if (!line || typeof line !== 'string') {
        return true;
    }

    // 去除空格后检查
    const trimmedLine = line.trim();

    // 完全空行
    if (trimmedLine === '') {
        return true;
    }

    // 只包含分隔符的行
    const onlyDelimiters = new RegExp('^[' + this.escapeRegExp(this.options.delimiter) + '\\s]*$');
    if (onlyDelimiters.test(trimmedLine)) {
        return true;
    }

    return false;
};

/**
 * @function CSVParser.prototype.validateResult
 * @description 验证解析结果
 * @param {Object} result - 解析结果
 * @returns {Object} 验证结果 {isValid: boolean, message: string}
 */
CSVParser.prototype.validateResult = function(result) {
    try {
        // 检查基本结构
        if (!result || typeof result !== 'object') {
            return { isValid: false, message: '解析结果格式无效' };
        }

        // 检查必要字段
        if (!Array.isArray(result.data)) {
            return { isValid: false, message: '数据字段无效' };
        }

        if (!Array.isArray(result.headers)) {
            return { isValid: false, message: '标题字段无效' };
        }

        // 检查数据一致性
        if (this.options.hasHeader && result.headers.length > 0) {
            // 检查每行数据的字段数量是否与标题一致
            for (let i = 0; i < Math.min(result.data.length, 10); i++) { // 只检查前10行
                const row = result.data[i];
                if (typeof row === 'object' && !Array.isArray(row)) {
                    // 对象格式，检查属性数量
                    const rowKeys = Object.keys(row);
                    if (rowKeys.length !== result.headers.length) {
                        SmartOffice.log('warn', `第${i + 1}行数据字段数量与标题不匹配`);
                    }
                }
            }
        }

        // 检查数据量限制
        if (result.data.length > 100000) { // 10万行限制
            return { isValid: false, message: '数据行数超过限制（最大10万行）' };
        }

        if (result.headers.length > 1000) { // 1000列限制
            return { isValid: false, message: '数据列数超过限制（最大1000列）' };
        }

        return { isValid: true, message: '验证通过' };

    } catch (error) {
        return { isValid: false, message: '验证过程出错: ' + error.message };
    }
};

/**
 * @function CSVParser.prototype.resetState
 * @description 重置解析器状态
 */
CSVParser.prototype.resetState = function() {
    this.isParsingInProgress = false;
    this.currentProgress = 0;
    this.totalBytes = 0;
    this.processedBytes = 0;
    this.headers = [];
    this.data = [];
    this.errors = [];
    this.statistics = {
        totalRows: 0,
        totalColumns: 0,
        emptyRows: 0,
        errorRows: 0,
        parseTime: 0
    };
};

/**
 * @function CSVParser.prototype.escapeRegExp
 * @description 转义正则表达式特殊字符
 * @param {string} string - 要转义的字符串
 * @returns {string} 转义后的字符串
 */
CSVParser.prototype.escapeRegExp = function(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

/**
 * @function CSVParser.prototype.formatBytes
 * @description 格式化字节数显示
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
CSVParser.prototype.formatBytes = function(bytes) {
    if (!bytes || bytes === 0) {
        return '0 B';
    }

    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }

    return Math.round(size * 100) / 100 + ' ' + units[unitIndex];
};

/**
 * @function CSVParser.prototype.getProgress
 * @description 获取当前解析进度
 * @returns {number} 进度百分比 (0-1)
 */
CSVParser.prototype.getProgress = function() {
    return this.currentProgress;
};

/**
 * @function CSVParser.prototype.isParsingInProgress
 * @description 检查是否正在解析
 * @returns {boolean} 是否正在解析
 */
CSVParser.prototype.isCurrentlyParsing = function() {
    return this.isParsingInProgress;
};

/**
 * @function CSVParser.prototype.getStatistics
 * @description 获取解析统计信息
 * @returns {Object} 统计信息对象
 */
CSVParser.prototype.getStatistics = function() {
    return this.statistics;
};

/**
 * @function CSVParser.prototype.getErrors
 * @description 获取解析错误列表
 * @returns {Array} 错误列表
 */
CSVParser.prototype.getErrors = function() {
    return this.errors;
};

/**
 * @function CSVParser.prototype.detectDelimiter
 * @description 自动检测CSV分隔符
 * @param {string} content - CSV内容样本（通常是前几行）
 * @returns {string} 检测到的分隔符
 */
CSVParser.prototype.detectDelimiter = function(content) {
    if (!content || typeof content !== 'string') {
        return this.options.delimiter;
    }

    // 常见分隔符候选
    const delimiters = [',', ';', '\t', '|'];
    const sampleLines = content.split(/\r\n|\n|\r/).slice(0, 5); // 取前5行作为样本

    let bestDelimiter = this.options.delimiter;
    let maxConsistency = 0;

    for (let i = 0; i < delimiters.length; i++) {
        const delimiter = delimiters[i];
        const fieldCounts = [];

        // 计算每行的字段数量
        for (let j = 0; j < sampleLines.length; j++) {
            const line = sampleLines[j].trim();
            if (line) {
                const fieldCount = line.split(delimiter).length;
                fieldCounts.push(fieldCount);
            }
        }

        // 计算一致性（相同字段数量的行数）
        if (fieldCounts.length > 0) {
            const mostCommonCount = this.getMostCommonValue(fieldCounts);
            const consistency = fieldCounts.filter(count => count === mostCommonCount).length;

            if (consistency > maxConsistency && mostCommonCount > 1) {
                maxConsistency = consistency;
                bestDelimiter = delimiter;
            }
        }
    }

    SmartOffice.log('info', '检测到CSV分隔符:', bestDelimiter);
    return bestDelimiter;
};

/**
 * @function CSVParser.prototype.getMostCommonValue
 * @description 获取数组中最常见的值
 * @param {Array} array - 数值数组
 * @returns {*} 最常见的值
 */
CSVParser.prototype.getMostCommonValue = function(array) {
    if (!Array.isArray(array) || array.length === 0) {
        return null;
    }

    const frequency = {};
    let maxCount = 0;
    let mostCommon = array[0];

    for (let i = 0; i < array.length; i++) {
        const value = array[i];
        frequency[value] = (frequency[value] || 0) + 1;

        if (frequency[value] > maxCount) {
            maxCount = frequency[value];
            mostCommon = value;
        }
    }

    return mostCommon;
};

/**
 * @function CSVParser.prototype.previewData
 * @description 预览CSV数据（只解析前几行）
 * @param {string} content - CSV内容
 * @param {number} maxRows - 最大预览行数（默认10）
 * @returns {Object} 预览结果
 */
CSVParser.prototype.previewData = function(content, maxRows) {
    maxRows = maxRows || 10;

    try {
        const lines = this.splitLines(content);
        const previewLines = lines.slice(0, maxRows + (this.options.hasHeader ? 1 : 0));
        const previewContent = previewLines.join('\n');

        // 临时修改选项以只解析预览数据
        const originalOptions = this.options;
        this.options = SmartOffice.Utils.Helpers.extend({}, originalOptions);

        // 同步解析预览数据
        const previewResult = this.parseContentSync(previewContent);

        // 恢复原始选项
        this.options = originalOptions;

        return {
            success: true,
            headers: previewResult.headers,
            data: previewResult.data.slice(0, maxRows),
            totalLines: lines.length,
            previewLines: Math.min(maxRows, previewResult.data.length)
        };

    } catch (error) {
        return {
            success: false,
            error: error.message,
            headers: [],
            data: []
        };
    }
};

/**
 * @function CSVParser.prototype.parseContentSync
 * @description 同步解析CSV内容（用于小数据量预览）
 * @param {string} content - CSV内容
 * @returns {Object} 解析结果
 */
CSVParser.prototype.parseContentSync = function(content) {
    const lines = this.splitLines(content);
    const headers = [];
    const data = [];

    let dataStartIndex = 0;
    if (this.options.hasHeader && lines.length > 0) {
        headers.push(...this.parseLine(lines[0], 0));
        dataStartIndex = 1;
    }

    for (let i = dataStartIndex; i < lines.length; i++) {
        const line = lines[i];
        if (!this.isEmptyLine(line)) {
            const rowData = this.parseLine(line, i);

            if (this.options.hasHeader && headers.length > 0) {
                const rowObject = {};
                for (let j = 0; j < headers.length; j++) {
                    rowObject[headers[j]] = rowData[j] || '';
                }
                data.push(rowObject);
            } else {
                data.push(rowData);
            }
        }
    }

    return { headers, data };
};

// 注册到全局命名空间
SmartOffice.Data.CSVParser = CSVParser;

SmartOffice.log('info', 'SmartOffice CSV解析器模块初始化完成');
