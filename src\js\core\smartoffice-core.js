/**
 * @file SmartOffice核心命名空间和基础设施
 * @description GoMyHire移动端快速透视分析 - 核心架构定义
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @namespace SmartOffice
 * @description 全局命名空间，包含所有应用模块
 */
window.SmartOffice = window.SmartOffice || {};

/**
 * @namespace SmartOffice.Config
 * @description 应用配置和常量定义
 */
SmartOffice.Config = {
    // 应用基础配置
    APP_NAME: 'GoMyHire透视分析',
    APP_VERSION: '1.0.0',
    DEBUG: true, // 开发模式标志
    
    // 文件处理配置
    MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
    SUPPORTED_FILE_TYPES: ['csv', 'txt'],
    CHUNK_SIZE: 1024 * 1024, // 1MB分块大小
    
    // UI配置
    ANIMATION_DURATION: 300, // iOS标准动画时长
    TOUCH_DELAY: 100, // 触摸反馈延迟
    DEBOUNCE_DELAY: 300, // 防抖延迟
    
    // 存储配置
    STORAGE_PREFIX: 'smartoffice_',
    MAX_CONFIGS: 50, // 最大配置数量
    
    // iOS风格配置
    IOS_COLORS: {
        PRIMARY: '#007AFF',      // iOS蓝色
        SUCCESS: '#34C759',      // iOS绿色
        WARNING: '#FF9500',      // iOS橙色
        DANGER: '#FF3B30',       // iOS红色
        BACKGROUND: '#F2F2F7',   // iOS背景色
        CARD_BACKGROUND: '#FFFFFF',
        TEXT_PRIMARY: '#000000',
        TEXT_SECONDARY: '#8E8E93',
        SEPARATOR: '#C6C6C8'
    },
    
    // 触摸反馈配置
    HAPTIC_FEEDBACK: {
        LIGHT: 'light',
        MEDIUM: 'medium',
        HEAVY: 'heavy'
    }
};

/**
 * @namespace SmartOffice.Core
 * @description 核心基础设施模块
 */
SmartOffice.Core = {
    /**
     * @property {Object} EventBus - 全局事件总线
     */
    EventBus: null,
    
    /**
     * @property {Object} Storage - 本地存储管理
     */
    Storage: null,
    
    /**
     * @property {Object} Router - 页面路由管理
     */
    Router: null,
    
    /**
     * @property {Object} App - 应用主控制器
     */
    App: null
};

/**
 * @namespace SmartOffice.Components
 * @description UI组件模块
 */
SmartOffice.Components = {
    /**
     * @property {Function} ConfigList - 配置列表组件
     */
    ConfigList: null,
    
    /**
     * @property {Function} ConfigForm - 配置表单组件
     */
    ConfigForm: null,
    
    /**
     * @property {Function} Dropdown - 下拉菜单组件
     */
    Dropdown: null,
    
    /**
     * @property {Function} DataTable - 数据表格组件
     */
    DataTable: null,
    
    /**
     * @property {Function} FileUpload - 文件上传组件
     */
    FileUpload: null,
    
    /**
     * @property {Function} Loading - 加载状态组件
     */
    Loading: null
};

/**
 * @namespace SmartOffice.Data
 * @description 数据处理模块
 */
SmartOffice.Data = {
    /**
     * @property {Object} CSVParser - CSV解析器
     */
    CSVParser: null,
    
    /**
     * @property {Object} PivotEngine - 透视表计算引擎
     */
    PivotEngine: null,
    
    /**
     * @property {Object} ConfigManager - 配置管理器
     */
    ConfigManager: null,
    
    /**
     * @property {Object} DataValidator - 数据验证器
     */
    DataValidator: null
};

/**
 * @namespace SmartOffice.Utils
 * @description 工具函数模块
 */
SmartOffice.Utils = {
    /**
     * @property {Object} Helpers - 通用工具函数
     */
    Helpers: null,
    
    /**
     * @property {Object} DOM - DOM操作工具
     */
    DOM: null,
    
    /**
     * @property {Object} Format - 数据格式化工具
     */
    Format: null
};

/**
 * @namespace SmartOffice.State
 * @description 应用状态管理
 */
SmartOffice.State = {
    /**
     * @property {string} currentPage - 当前页面
     */
    currentPage: 'configList',
    
    /**
     * @property {Array} configs - 透视表配置列表
     */
    configs: [],
    
    /**
     * @property {Object} currentConfig - 当前编辑的配置
     */
    currentConfig: null,
    
    /**
     * @property {Object} uploadedData - 上传的数据
     */
    uploadedData: null,
    
    /**
     * @property {boolean} isLoading - 加载状态
     */
    isLoading: false,
    
    /**
     * @property {Object} ui - UI状态
     */
    ui: {
        navTitle: '透视分析',
        showBackButton: false,
        showAddButton: true
    }
};

/**
 * @function SmartOffice.log
 * @description 统一的日志输出函数
 * @param {string} level - 日志级别 (info, warn, error)
 * @param {string} message - 日志消息
 * @param {*} data - 附加数据
 */
SmartOffice.log = function(level, message, data) {
    if (!SmartOffice.Config.DEBUG && level === 'info') {
        return; // 生产模式下不输出info日志
    }
    
    const timestamp = new Date().toLocaleTimeString();
    const prefix = `[${timestamp}] SmartOffice`;
    
    switch (level) {
        case 'info':
            console.log(`${prefix} ℹ️ ${message}`, data || '');
            break;
        case 'warn':
            console.warn(`${prefix} ⚠️ ${message}`, data || '');
            break;
        case 'error':
            console.error(`${prefix} ❌ ${message}`, data || '');
            break;
        default:
            console.log(`${prefix} ${message}`, data || '');
    }
};

/**
 * @function SmartOffice.ready
 * @description 检查所有模块是否已加载完成
 * @returns {boolean} 是否所有模块都已就绪
 */
SmartOffice.ready = function() {
    const requiredModules = [
        'Core.EventBus',
        'Core.Storage', 
        'Core.Router',
        'Core.App',
        'Utils.Helpers',
        'Utils.DOM',
        'Components.ConfigList'
    ];
    
    for (let i = 0; i < requiredModules.length; i++) {
        const modulePath = requiredModules[i].split('.');
        let module = SmartOffice;
        
        for (let j = 0; j < modulePath.length; j++) {
            module = module[modulePath[j]];
            if (!module) {
                SmartOffice.log('warn', `模块 ${requiredModules[i]} 未加载`);
                return false;
            }
        }
    }
    
    return true;
};

/**
 * @function SmartOffice.triggerHapticFeedback
 * @description 触发iOS触觉反馈（如果支持）
 * @param {string} type - 反馈类型 (light, medium, heavy)
 */
SmartOffice.triggerHapticFeedback = function(type) {
    if (window.navigator && window.navigator.vibrate) {
        // 在支持的设备上触发振动反馈
        const patterns = {
            light: [10],
            medium: [20],
            heavy: [30]
        };
        window.navigator.vibrate(patterns[type] || patterns.light);
    }
    
    // 如果支持iOS的触觉反馈API
    if (window.DeviceMotionEvent && typeof DeviceMotionEvent.requestPermission === 'function') {
        // iOS 13+的触觉反馈处理
        SmartOffice.log('info', `触发${type}级别触觉反馈`);
    }
};

// 初始化完成标志
SmartOffice._coreInitialized = true;
SmartOffice.log('info', 'SmartOffice核心命名空间初始化完成');

// 导出到全局作用域（兼容性处理）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SmartOffice;
}

// 防止意外覆盖
Object.freeze(SmartOffice.Config);
Object.freeze(SmartOffice.Config.IOS_COLORS);
Object.freeze(SmartOffice.Config.HAPTIC_FEEDBACK);
