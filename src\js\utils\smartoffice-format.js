/**
 * @file SmartOffice数据格式化工具
 * @description 提供数据格式化和显示相关的工具函数
 * <AUTHOR> Team
 */

/**
 * @namespace Format
 * @description 数据格式化工具函数集合
 */
function Format() {
    SmartOffice.log('info', 'Format工具函数初始化完成');
}

/**
 * @function Format.currency
 * @description 格式化货币显示
 * @param {number} amount - 金额
 * @param {string} currency - 货币代码（默认CNY）
 * @returns {string} 格式化后的货币字符串
 */
Format.currency = function(amount, currency) {
    const code = currency || 'CNY';
    const num = parseFloat(amount) || 0;
    
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: code
    }).format(num);
};

/**
 * @function Format.percentage
 * @description 格式化百分比显示
 * @param {number} value - 数值
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的百分比字符串
 */
Format.percentage = function(value, decimals) {
    const num = parseFloat(value) || 0;
    const dm = decimals || 2;
    
    return (num * 100).toFixed(dm) + '%';
};

// 注册到全局命名空间
SmartOffice.Utils.Format = Format;

SmartOffice.log('info', 'SmartOffice格式化工具模块初始化完成');
