/**
 * @file SmartOffice本地存储管理系统
 * @description 封装localStorage，提供配置数据的持久化存储
 * <AUTHOR> Team
 */

/**
 * @function StorageManager
 * @description 存储管理器构造函数
 * @constructor
 */
function StorageManager() {
    /**
     * @property {string} prefix - 存储键名前缀
     * @private
     */
    this.prefix = SmartOffice.Config.STORAGE_PREFIX;
    
    /**
     * @property {boolean} available - localStorage是否可用
     * @private
     */
    this.available = this.checkAvailability();
    
    /**
     * @property {Object} cache - 内存缓存
     * @private
     */
    this.cache = {};
    
    SmartOffice.log('info', `StorageManager初始化完成，localStorage可用: ${this.available}`);
}

/**
 * @function StorageManager.prototype.checkAvailability
 * @description 检查localStorage是否可用
 * @returns {boolean} 是否可用
 * @private
 */
StorageManager.prototype.checkAvailability = function() {
    try {
        const testKey = this.prefix + 'test';
        localStorage.setItem(testKey, 'test');
        localStorage.removeItem(testKey);
        return true;
    } catch (error) {
        SmartOffice.log('warn', 'localStorage不可用，将使用内存存储', error);
        return false;
    }
};

/**
 * @function StorageManager.prototype.getKey
 * @description 生成带前缀的存储键名
 * @param {string} key - 原始键名
 * @returns {string} 带前缀的键名
 * @private
 */
StorageManager.prototype.getKey = function(key) {
    return this.prefix + key;
};

/**
 * @function StorageManager.prototype.set
 * @description 存储数据
 * @param {string} key - 键名
 * @param {*} value - 要存储的值
 * @returns {boolean} 是否存储成功
 */
StorageManager.prototype.set = function(key, value) {
    if (typeof key !== 'string' || !key.trim()) {
        SmartOffice.log('error', 'Storage.set: 键名必须是非空字符串');
        return false;
    }
    
    try {
        const serializedValue = JSON.stringify(value);
        const storageKey = this.getKey(key);
        
        if (this.available) {
            localStorage.setItem(storageKey, serializedValue);
        }
        
        // 同时更新内存缓存
        this.cache[key] = value;
        
        SmartOffice.log('info', `数据已存储: ${key}`);
        return true;
        
    } catch (error) {
        SmartOffice.log('error', `存储数据失败: ${key}`, error);
        return false;
    }
};

/**
 * @function StorageManager.prototype.get
 * @description 获取存储的数据
 * @param {string} key - 键名
 * @param {*} defaultValue - 默认值
 * @returns {*} 存储的值或默认值
 */
StorageManager.prototype.get = function(key, defaultValue) {
    if (typeof key !== 'string' || !key.trim()) {
        SmartOffice.log('error', 'Storage.get: 键名必须是非空字符串');
        return defaultValue;
    }
    
    // 优先从内存缓存获取
    if (this.cache.hasOwnProperty(key)) {
        return this.cache[key];
    }
    
    try {
        const storageKey = this.getKey(key);
        let value = null;
        
        if (this.available) {
            value = localStorage.getItem(storageKey);
        }
        
        if (value === null) {
            return defaultValue;
        }
        
        const parsedValue = JSON.parse(value);
        
        // 更新内存缓存
        this.cache[key] = parsedValue;
        
        return parsedValue;
        
    } catch (error) {
        SmartOffice.log('error', `获取数据失败: ${key}`, error);
        return defaultValue;
    }
};

/**
 * @function StorageManager.prototype.remove
 * @description 删除存储的数据
 * @param {string} key - 键名
 * @returns {boolean} 是否删除成功
 */
StorageManager.prototype.remove = function(key) {
    if (typeof key !== 'string' || !key.trim()) {
        SmartOffice.log('error', 'Storage.remove: 键名必须是非空字符串');
        return false;
    }
    
    try {
        const storageKey = this.getKey(key);
        
        if (this.available) {
            localStorage.removeItem(storageKey);
        }
        
        // 从内存缓存中删除
        delete this.cache[key];
        
        SmartOffice.log('info', `数据已删除: ${key}`);
        return true;
        
    } catch (error) {
        SmartOffice.log('error', `删除数据失败: ${key}`, error);
        return false;
    }
};

/**
 * @function StorageManager.prototype.clear
 * @description 清除所有应用相关的存储数据
 * @returns {boolean} 是否清除成功
 */
StorageManager.prototype.clear = function() {
    try {
        if (this.available) {
            // 只删除带有应用前缀的键
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(this.prefix)) {
                    keysToRemove.push(key);
                }
            }
            
            for (let i = 0; i < keysToRemove.length; i++) {
                localStorage.removeItem(keysToRemove[i]);
            }
        }
        
        // 清除内存缓存
        this.cache = {};
        
        SmartOffice.log('info', '所有应用数据已清除');
        return true;
        
    } catch (error) {
        SmartOffice.log('error', '清除数据失败', error);
        return false;
    }
};

/**
 * @function StorageManager.prototype.exists
 * @description 检查指定键是否存在
 * @param {string} key - 键名
 * @returns {boolean} 是否存在
 */
StorageManager.prototype.exists = function(key) {
    if (typeof key !== 'string' || !key.trim()) {
        return false;
    }
    
    // 检查内存缓存
    if (this.cache.hasOwnProperty(key)) {
        return true;
    }
    
    // 检查localStorage
    if (this.available) {
        const storageKey = this.getKey(key);
        return localStorage.getItem(storageKey) !== null;
    }
    
    return false;
};

/**
 * @function StorageManager.prototype.getSize
 * @description 获取存储使用的大小（字节）
 * @returns {number} 存储大小
 */
StorageManager.prototype.getSize = function() {
    if (!this.available) {
        return 0;
    }
    
    let totalSize = 0;
    
    try {
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(this.prefix)) {
                const value = localStorage.getItem(key);
                totalSize += key.length + (value ? value.length : 0);
            }
        }
    } catch (error) {
        SmartOffice.log('error', '计算存储大小失败', error);
    }
    
    return totalSize;
};

/**
 * @function StorageManager.prototype.getAllKeys
 * @description 获取所有应用相关的键名
 * @returns {Array<string>} 键名数组
 */
StorageManager.prototype.getAllKeys = function() {
    const keys = [];
    
    // 从内存缓存获取
    for (const key in this.cache) {
        if (this.cache.hasOwnProperty(key)) {
            keys.push(key);
        }
    }
    
    // 从localStorage获取
    if (this.available) {
        try {
            for (let i = 0; i < localStorage.length; i++) {
                const storageKey = localStorage.key(i);
                if (storageKey && storageKey.startsWith(this.prefix)) {
                    const key = storageKey.substring(this.prefix.length);
                    if (keys.indexOf(key) === -1) {
                        keys.push(key);
                    }
                }
            }
        } catch (error) {
            SmartOffice.log('error', '获取键名列表失败', error);
        }
    }
    
    return keys;
};

// 创建全局存储管理器实例
SmartOffice.Core.Storage = new StorageManager();

// 定义标准的存储键名
SmartOffice.StorageKeys = {
    CONFIGS: 'configs',              // 透视表配置列表
    CURRENT_CONFIG: 'currentConfig', // 当前编辑的配置
    APP_SETTINGS: 'appSettings',     // 应用设置
    USER_PREFERENCES: 'userPrefs',   // 用户偏好设置
    LAST_UPLOAD_DATA: 'lastUpload'   // 最后上传的数据信息
};

// 冻结存储键名对象
Object.freeze(SmartOffice.StorageKeys);

SmartOffice.log('info', 'SmartOffice存储系统初始化完成');
