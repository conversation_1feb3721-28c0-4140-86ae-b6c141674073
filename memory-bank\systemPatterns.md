# System Patterns: GoMyHire 移动端快速透视分析 - 配置管理系统完成

## 1. 系统架构 (System Architecture) - 2025-01-03 更新

本项目采用纯原生前端双页面应用架构，使用传统JavaScript实现，完全在用户浏览器端运行，无第三方依赖。

### ✅ 已实现的核心架构组件
- **SmartOffice全局命名空间**: 完整的模块化架构设计
- **页面路由系统**: iOS风格的页面切换和导航管理
- **事件总线系统**: 发布-订阅模式的组件通信
- **本地存储管理**: localStorage封装和数据持久化
- **配置管理系统**: 完整的CRUD操作和数据验证

```mermaid
graph TD
    subgraph 用户设备 (浏览器)
        A[主界面 - 配置列表] --> B[SmartOffice.Core.Router]
        B --> C[配置页面 - 透视表设置]

        A --> D[SmartOffice.Data.ConfigManager]
        C --> D
        D --> E[SmartOffice.Core.Storage]

        A --> F[SmartOffice.Components.FileUpload]
        F --> G[Web Worker - 文件解析]
        G --> H[SmartOffice.Data.CSVParser]
        H --> I[数据类型识别器]

        G --> J[SmartOffice.Data.PivotEngine]
        J --> K[原生分组算法]
        J --> L[聚合计算器]
        J --> M[结果生成器]

        M --> N[SmartOffice.Components.DataTable]
        N --> A

        O[SmartOffice.Core.EventBus] --> A
        O --> C
        O --> D
    end

    用户 --> A
```

### 全局命名空间设计 (SmartOffice):

```javascript
window.SmartOffice = {
  // 核心基础设施模块
  Core: {
    Router: {},        // 页面路由管理器
    EventBus: {},      // 全局事件总线
    Storage: {},       // localStorage封装
    App: {}           // 应用主控制器
  },

  // UI组件模块
  Components: {
    ConfigList: {},    // 配置列表组件
    ConfigForm: {},    // 配置表单组件
    Dropdown: {},      // 移动端下拉菜单
    DataTable: {},     // 数据表格显示
    FileUpload: {},    // 文件上传组件
    LoadingSpinner: {} // 加载状态组件
  },

  // 数据处理模块
  Data: {
    CSVParser: {},     // CSV文件解析器
    PivotEngine: {},   // 透视表计算引擎
    ConfigManager: {}, // 配置管理器
    DataValidator: {}  // 数据验证器
  },

  // 工具函数模块
  Utils: {
    Helpers: {},       // 通用工具函数
    DOM: {},          // DOM操作工具
    Events: {},       // 事件处理工具
    Format: {}        // 数据格式化工具
  }
};
```

### ✅ 已实现架构组件说明 (传统JavaScript实现)

#### 核心基础设施 (100% 完成)
- **SmartOffice.Core.Router**: ✅ 完整的页面路由系统，iOS风格切换动画
- **SmartOffice.Core.EventBus**: ✅ 发布-订阅模式的事件总线系统
- **SmartOffice.Core.Storage**: ✅ localStorage封装，提供统一存储接口
- **SmartOffice.Core.App**: ✅ 应用主控制器，管理组件生命周期

#### UI组件系统 (100% 完成)
- **SmartOffice.Components.ConfigList**: ✅ iOS风格配置列表组件
- **SmartOffice.Components.ConfigForm**: ✅ 完整的配置表单组件
- **双页面应用架构**: ✅ 主界面(配置列表) + 配置页面(表单设置)

#### 🚧 待实现组件 (下一阶段)
- **SmartOffice.Components.FieldSelector**: 字段选择器组件
- **SmartOffice.Components.FileUpload**: 文件上传组件
- **SmartOffice.Data.CSVParser**: CSV解析器
- **SmartOffice.Data.PivotEngine**: 透视表计算引擎
- **SmartOffice.Components.DataTable**: 数据表格显示组件

## 2. 关键技术决策 (Key Technical Decisions) - 传统JavaScript方案

### 核心原则: 零第三方依赖 + 传统实现
- **无框架**: 使用原生HTML5 + CSS3 + 传统JavaScript
- **无构建工具**: 直接浏览器执行，传统`<script>`标签加载
- **无外部库**: 所有功能自行实现或使用浏览器原生API
- **传统模块化**: 使用全局命名空间 + 构造函数模式

### 技术选择说明
- **模块化**: 全局命名空间SmartOffice，避免ES6模块
- **文件解析**: 自实现CSV解析器，纯JavaScript实现
- **数据处理**: 原生Array方法(map, filter, reduce)实现透视表计算
- **状态管理**: 全局对象 + DOM事件 + 自定义事件系统
- **UI组件**: 构造函数模式 + 原生CSS + DOM操作
- **存储方案**: localStorage封装，满足配置存储需求
- **性能优化**: Web Workers + 算法优化 + 内存管理

## 3. 设计模式 (Design Patterns) - 传统JavaScript实现

### 3.1 传统模块化设计
```javascript
// 传统JavaScript文件结构
src/
├── js/
│   ├── core/
│   │   ├── smartoffice-core.js      // 核心命名空间和基础设施
│   │   ├── smartoffice-router.js    // 路由管理
│   │   ├── smartoffice-events.js    // 事件总线
│   │   └── smartoffice-storage.js   // 存储管理
│   ├── components/
│   │   ├── smartoffice-config-list.js   // 配置列表组件
│   │   ├── smartoffice-config-form.js   // 配置表单组件
│   │   ├── smartoffice-dropdown.js      // 下拉菜单组件
│   │   └── smartoffice-data-table.js    // 数据表格组件
│   ├── data/
│   │   ├── smartoffice-csv-parser.js    // CSV解析器
│   │   ├── smartoffice-pivot-engine.js  // 透视表引擎
│   │   └── smartoffice-config-manager.js // 配置管理器
│   └── workers/
│       └── data-processor.js            // Web Worker文件处理
```

### 3.2 构造函数模式 (组件系统)
```javascript
// 传统构造函数模式组件
function ConfigListComponent(container) {
  this.container = container;
  this.configs = [];
  this.eventBus = SmartOffice.Core.EventBus;

  // 初始化组件
  this.init();
}

ConfigListComponent.prototype.init = function() {
  this.render();
  this.bindEvents();
};

ConfigListComponent.prototype.render = function() {
  // 渲染逻辑
};

ConfigListComponent.prototype.bindEvents = function() {
  // 事件绑定
};

// 注册到全局命名空间
SmartOffice.Components.ConfigList = ConfigListComponent;
```

### 3.3 观察者模式 (事件驱动)
```javascript
// 传统JavaScript事件系统
function EventBus() {
  this.events = {};
}

EventBus.prototype.on = function(event, callback) {
  if (!this.events[event]) {
    this.events[event] = [];
  }
  this.events[event].push(callback);
};

EventBus.prototype.emit = function(event, data) {
  if (this.events[event]) {
    this.events[event].forEach(function(callback) {
      callback(data);
    });
  }
};

// 全局事件总线实例
SmartOffice.Core.EventBus = new EventBus();
```
-   **单向数据流**: (如果使用React/Vue等) 保持清晰的数据流向，便于调试和维护。
-   **观察者模式**: 用于当数据更新时，通知相关的透视表小部件进行刷新。
-   **策略模式**: (可选) 如果支持多种数据源或多种透视表计算策略，可以使用此模式。

## 4. 性能优化策略
-   **Web Workers**: 将文件解析和复杂计算移至后台线程。
-   **虚拟列表/滚动**: 如果透视表结果集很大，使用虚拟滚动技术优化渲染性能。
-   **代码分割/懒加载**: 按需加载模块和组件，减少初始加载时间。
-   **数据结构优化**: 选择高效的数据结构存储和处理内存中的数据。
-   **节流/防抖**: 对频繁触发的事件（如窗口调整、用户输入）进行处理。
-   **Memoization**: 缓存计算结果，避免重复计算。

## 5. 数据流 (Data Flow for Core Scenario)
1.  用户通过UI上传文件。
2.  文件传递给 Web Worker。
3.  Web Worker 使用 SheetJS 解析文件，生成结构化数据。
4.  解析后的数据（或其引用/状态）传递回主线程的核心逻辑层。
5.  核心逻辑层通知配置管理模块获取所有预设的透视表配置。
6.  对于每个配置：
    *   核心逻辑层将数据和配置传递给透视表引擎（可能部分计算在Web Worker中进行）。
    *   透视表引擎计算结果。
7.  核心逻辑层将计算结果传递给UI层。
8.  UI层更新对应的透视表小部件。
