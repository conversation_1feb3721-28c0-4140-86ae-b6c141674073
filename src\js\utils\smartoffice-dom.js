/**
 * @file SmartOffice DOM操作工具
 * @description 提供DOM操作的便捷方法和iOS风格的交互效果
 * <AUTHOR> Team
 */

/**
 * @namespace DOM
 * @description DOM操作工具函数集合
 */
function DOM() {
    SmartOffice.log('info', 'DOM工具函数初始化完成');
}

/**
 * @function DOM.querySelector
 * @description 安全的querySelector，带错误处理
 * @param {string} selector - CSS选择器
 * @param {Element} context - 查找上下文（可选）
 * @returns {Element|null} 找到的元素或null
 */
DOM.querySelector = function(selector, context) {
    try {
        const root = context || document;
        return root.querySelector(selector);
    } catch (error) {
        SmartOffice.log('error', 'querySelector失败:', error);
        return null;
    }
};

/**
 * @function DOM.querySelectorAll
 * @description 安全的querySelectorAll，返回数组
 * @param {string} selector - CSS选择器
 * @param {Element} context - 查找上下文（可选）
 * @returns {Array<Element>} 元素数组
 */
DOM.querySelectorAll = function(selector, context) {
    try {
        const root = context || document;
        return Array.prototype.slice.call(root.querySelectorAll(selector));
    } catch (error) {
        SmartOffice.log('error', 'querySelectorAll失败:', error);
        return [];
    }
};

/**
 * @function DOM.createElement
 * @description 创建DOM元素并设置属性
 * @param {string} tagName - 标签名
 * @param {Object} attributes - 属性对象
 * @param {string} textContent - 文本内容（可选）
 * @returns {Element} 创建的元素
 */
DOM.createElement = function(tagName, attributes, textContent) {
    const element = document.createElement(tagName);
    
    if (attributes) {
        for (const key in attributes) {
            if (attributes.hasOwnProperty(key)) {
                if (key === 'className') {
                    element.className = attributes[key];
                } else if (key === 'dataset') {
                    for (const dataKey in attributes[key]) {
                        element.dataset[dataKey] = attributes[key][dataKey];
                    }
                } else {
                    element.setAttribute(key, attributes[key]);
                }
            }
        }
    }
    
    if (textContent) {
        element.textContent = textContent;
    }
    
    return element;
};

/**
 * @function DOM.addClass
 * @description 添加CSS类名
 * @param {Element} element - 目标元素
 * @param {string|Array} className - 类名或类名数组
 */
DOM.addClass = function(element, className) {
    if (!element) return;
    
    if (Array.isArray(className)) {
        for (let i = 0; i < className.length; i++) {
            element.classList.add(className[i]);
        }
    } else {
        element.classList.add(className);
    }
};

/**
 * @function DOM.removeClass
 * @description 移除CSS类名
 * @param {Element} element - 目标元素
 * @param {string|Array} className - 类名或类名数组
 */
DOM.removeClass = function(element, className) {
    if (!element) return;
    
    if (Array.isArray(className)) {
        for (let i = 0; i < className.length; i++) {
            element.classList.remove(className[i]);
        }
    } else {
        element.classList.remove(className);
    }
};

/**
 * @function DOM.toggleClass
 * @description 切换CSS类名
 * @param {Element} element - 目标元素
 * @param {string} className - 类名
 * @param {boolean} force - 强制添加或移除（可选）
 * @returns {boolean} 是否包含该类名
 */
DOM.toggleClass = function(element, className, force) {
    if (!element) return false;
    
    return element.classList.toggle(className, force);
};

/**
 * @function DOM.hasClass
 * @description 检查是否包含CSS类名
 * @param {Element} element - 目标元素
 * @param {string} className - 类名
 * @returns {boolean} 是否包含
 */
DOM.hasClass = function(element, className) {
    if (!element) return false;
    
    return element.classList.contains(className);
};

/**
 * @function DOM.setStyle
 * @description 设置元素样式
 * @param {Element} element - 目标元素
 * @param {Object|string} styles - 样式对象或样式属性名
 * @param {string} value - 样式值（当styles为字符串时）
 */
DOM.setStyle = function(element, styles, value) {
    if (!element) return;
    
    if (typeof styles === 'string') {
        element.style[styles] = value;
    } else if (typeof styles === 'object') {
        for (const property in styles) {
            if (styles.hasOwnProperty(property)) {
                element.style[property] = styles[property];
            }
        }
    }
};

/**
 * @function DOM.getStyle
 * @description 获取元素计算样式
 * @param {Element} element - 目标元素
 * @param {string} property - 样式属性名
 * @returns {string} 样式值
 */
DOM.getStyle = function(element, property) {
    if (!element) return '';
    
    return window.getComputedStyle(element)[property];
};

/**
 * @function DOM.show
 * @description 显示元素
 * @param {Element} element - 目标元素
 * @param {string} display - 显示类型（可选，默认为'block'）
 */
DOM.show = function(element, display) {
    if (!element) return;
    
    element.style.display = display || 'block';
};

/**
 * @function DOM.hide
 * @description 隐藏元素
 * @param {Element} element - 目标元素
 */
DOM.hide = function(element) {
    if (!element) return;
    
    element.style.display = 'none';
};

/**
 * @function DOM.fadeIn
 * @description iOS风格淡入动画
 * @param {Element} element - 目标元素
 * @param {number} duration - 动画时长（毫秒）
 * @param {Function} callback - 完成回调
 */
DOM.fadeIn = function(element, duration, callback) {
    if (!element) return;
    
    const ms = duration || SmartOffice.Config.ANIMATION_DURATION;
    
    element.style.opacity = '0';
    element.style.display = 'block';
    element.style.transition = 'opacity ' + ms + 'ms ease';
    
    // 强制重绘
    element.offsetHeight;
    
    element.style.opacity = '1';
    
    setTimeout(function() {
        element.style.transition = '';
        if (callback) callback();
    }, ms);
};

/**
 * @function DOM.fadeOut
 * @description iOS风格淡出动画
 * @param {Element} element - 目标元素
 * @param {number} duration - 动画时长（毫秒）
 * @param {Function} callback - 完成回调
 */
DOM.fadeOut = function(element, duration, callback) {
    if (!element) return;
    
    const ms = duration || SmartOffice.Config.ANIMATION_DURATION;
    
    element.style.transition = 'opacity ' + ms + 'ms ease';
    element.style.opacity = '0';
    
    setTimeout(function() {
        element.style.display = 'none';
        element.style.transition = '';
        if (callback) callback();
    }, ms);
};

/**
 * @function DOM.slideIn
 * @description iOS风格滑入动画
 * @param {Element} element - 目标元素
 * @param {string} direction - 方向 ('left', 'right', 'up', 'down')
 * @param {number} duration - 动画时长（毫秒）
 * @param {Function} callback - 完成回调
 */
DOM.slideIn = function(element, direction, duration, callback) {
    if (!element) return;
    
    const ms = duration || SmartOffice.Config.ANIMATION_DURATION;
    const transforms = {
        left: 'translateX(-100%)',
        right: 'translateX(100%)',
        up: 'translateY(-100%)',
        down: 'translateY(100%)'
    };
    
    element.style.transform = transforms[direction] || transforms.right;
    element.style.display = 'block';
    element.style.transition = 'transform ' + ms + 'ms cubic-bezier(0.4, 0.0, 0.2, 1)';
    
    // 强制重绘
    element.offsetHeight;
    
    element.style.transform = 'translateX(0) translateY(0)';
    
    setTimeout(function() {
        element.style.transition = '';
        if (callback) callback();
    }, ms);
};

/**
 * @function DOM.addTouchFeedback
 * @description 为元素添加iOS风格的触摸反馈
 * @param {Element} element - 目标元素
 * @param {string} type - 反馈类型 ('light', 'medium', 'heavy')
 */
DOM.addTouchFeedback = function(element, type) {
    if (!element) return;
    
    const feedbackType = type || 'light';
    
    element.addEventListener('touchstart', function() {
        DOM.addClass(element, 'haptic-' + feedbackType);
        SmartOffice.triggerHapticFeedback(feedbackType);
    });
    
    element.addEventListener('touchend', function() {
        setTimeout(function() {
            DOM.removeClass(element, 'haptic-' + feedbackType);
        }, 100);
    });
    
    element.addEventListener('touchcancel', function() {
        DOM.removeClass(element, 'haptic-' + feedbackType);
    });
};

/**
 * @function DOM.scrollToTop
 * @description 平滑滚动到顶部
 * @param {Element} element - 滚动容器（可选，默认为window）
 * @param {number} duration - 动画时长（毫秒）
 */
DOM.scrollToTop = function(element, duration) {
    const target = element || window;
    const ms = duration || 500;
    const start = target.scrollY || target.scrollTop || 0;
    const startTime = performance.now();
    
    function animateScroll(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / ms, 1);
        
        // 使用easeOutCubic缓动函数
        const easeProgress = 1 - Math.pow(1 - progress, 3);
        const currentScroll = start * (1 - easeProgress);
        
        if (target === window) {
            window.scrollTo(0, currentScroll);
        } else {
            target.scrollTop = currentScroll;
        }
        
        if (progress < 1) {
            requestAnimationFrame(animateScroll);
        }
    }
    
    requestAnimationFrame(animateScroll);
};

/**
 * @function DOM.getElementPosition
 * @description 获取元素相对于视口的位置
 * @param {Element} element - 目标元素
 * @returns {Object} 位置信息对象
 */
DOM.getElementPosition = function(element) {
    if (!element) {
        return { top: 0, left: 0, width: 0, height: 0 };
    }
    
    const rect = element.getBoundingClientRect();
    return {
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height,
        right: rect.right,
        bottom: rect.bottom
    };
};

/**
 * @function DOM.isElementInViewport
 * @description 检查元素是否在视口内
 * @param {Element} element - 目标元素
 * @returns {boolean} 是否在视口内
 */
DOM.isElementInViewport = function(element) {
    if (!element) return false;
    
    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
};

// 注册到全局命名空间
SmartOffice.Utils.DOM = DOM;

SmartOffice.log('info', 'SmartOffice DOM工具模块初始化完成');
