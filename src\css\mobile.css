/**
 * @file 移动端响应式样式
 * @description 专门针对移动设备的样式优化
 * <AUTHOR> Team
 */

/* 移动端基础设置 */
@media screen and (max-width: 768px) {
    /* 防止iOS Safari的缩放 */
    html {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
    }
    
    /* 优化触摸滚动 */
    body {
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: none;
    }
    
    /* 移动端字体优化 */
    body {
        font-size: 16px; /* 防止iOS Safari自动缩放 */
    }
}

/* 超小屏幕设备 (iPhone SE等) */
@media screen and (max-width: 375px) {
    :root {
        --spacing-xs: 2px;
        --spacing-sm: 6px;
        --spacing-md: 12px;
        --spacing-lg: 18px;
        --spacing-xl: 24px;
        --spacing-xxl: 36px;
    }
    
    .nav-content {
        padding: 0 var(--spacing-sm);
    }
    
    .nav-title {
        font-size: var(--font-size-callout);
    }
    
    .page-content {
        padding: var(--spacing-sm);
    }
    
    .config-card-header {
        padding: var(--spacing-sm);
    }
    
    .config-card-content {
        padding: var(--spacing-sm);
    }
    
    .config-card-title {
        font-size: var(--font-size-callout);
    }
    
    .empty-state {
        padding: var(--spacing-xl) var(--spacing-sm);
        min-height: 250px;
    }
    
    .empty-icon {
        width: 48px;
        height: 48px;
        margin-bottom: var(--spacing-md);
    }
    
    .empty-title {
        font-size: var(--font-size-body);
    }
    
    .empty-description {
        font-size: var(--font-size-footnote);
        max-width: 240px;
    }
}

/* 中等屏幕设备 (iPhone 12等) */
@media screen and (min-width: 376px) and (max-width: 414px) {
    .config-card-meta {
        gap: var(--spacing-md);
    }
    
    .config-card-meta-item {
        min-width: 100px;
    }
}

/* 大屏幕设备 (iPhone 12 Pro Max等) */
@media screen and (min-width: 415px) and (max-width: 768px) {
    .config-list {
        gap: var(--spacing-lg);
    }
    
    .config-card-meta {
        gap: var(--spacing-lg);
    }
    
    .config-card-meta-item {
        min-width: 120px;
    }
}

/* 横屏模式优化 */
@media screen and (orientation: landscape) and (max-height: 500px) {
    .nav-bar {
        height: 40px;
    }
    
    .nav-content {
        height: 40px;
    }
    
    .nav-button {
        width: 40px;
        height: 40px;
    }
    
    .page-content {
        padding: var(--spacing-sm);
    }
    
    .empty-state {
        min-height: 200px;
        padding: var(--spacing-lg) var(--spacing-md);
    }
    
    .empty-icon {
        width: 40px;
        height: 40px;
        margin-bottom: var(--spacing-sm);
    }
    
    .config-card-header {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .config-card-content {
        padding: var(--spacing-sm) var(--spacing-md);
    }
}

/* iOS安全区域适配 */
@supports (padding: max(0px)) {
    .status-bar-spacer {
        height: max(env(safe-area-inset-top), 20px);
    }
    
    .page-content {
        padding-left: max(var(--spacing-md), env(safe-area-inset-left));
        padding-right: max(var(--spacing-md), env(safe-area-inset-right));
        padding-bottom: max(var(--spacing-md), env(safe-area-inset-bottom));
    }
    
    .nav-content {
        padding-left: max(var(--spacing-md), env(safe-area-inset-left));
        padding-right: max(var(--spacing-md), env(safe-area-inset-right));
    }
}

/* 触摸优化 */
@media (pointer: coarse) {
    /* 增大触摸目标 */
    .nav-button {
        min-width: 44px;
        min-height: 44px;
    }
    
    .config-card-action {
        min-width: 44px;
        min-height: 44px;
    }
    
    .ios-button {
        min-height: 44px;
        padding: 12px 24px;
    }
    
    .ios-list-item {
        min-height: 44px;
        padding: var(--spacing-md);
    }
    
    /* 优化触摸反馈 */
    .touchable:active {
        transform: scale(0.97);
    }
    
    .config-card:active {
        transform: scale(0.97);
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* 优化边框显示 */
    .nav-bar {
        border-bottom-width: 0.5px;
    }
    
    .config-card-header {
        border-bottom-width: 0.5px;
    }
    
    .ios-list-item {
        border-bottom-width: 0.5px;
    }
    
    .ios-form-row {
        border-bottom-width: 0.5px;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .page {
        transition: none;
    }
    
    .config-card {
        transition: none;
    }
    
    .touchable:active {
        transform: none;
    }
    
    .spinner-ring {
        animation: none;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --text-secondary: #000000;
        --text-tertiary: #000000;
        --separator-opaque: #000000;
    }
    
    .config-card {
        border: 2px solid var(--text-primary);
    }
    
    .nav-bar {
        border-bottom: 2px solid var(--text-primary);
    }
    
    .config-card-header {
        border-bottom: 1px solid var(--text-primary);
    }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    :root {
        --background-primary: #000000;
        --background-secondary: #1C1C1E;
        --background-tertiary: #2C2C2E;
        --background-grouped: #000000;
        
        --text-primary: #FFFFFF;
        --text-secondary: #8E8E93;
        --text-tertiary: #48484A;
        --text-quaternary: #3A3A3C;
        
        --separator-opaque: #38383A;
        --separator-non-opaque: rgba(84, 84, 88, 0.65);
    }
    
    .status-bar-spacer {
        background-color: var(--background-secondary);
    }
    
    .nav-bar {
        background-color: rgba(28, 28, 30, 0.8);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
    }
    
    .loading-overlay {
        background-color: rgba(0, 0, 0, 0.6);
    }
    
    .ios-toast {
        background-color: rgba(28, 28, 30, 0.9);
    }
}

/* 网络状态优化 */
@media (prefers-reduced-data: reduce) {
    /* 减少不必要的视觉效果 */
    .nav-bar {
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
    }
    
    .loading-overlay {
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
    }
    
    .ios-toast {
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
    }
}

/* 键盘弹出时的适配 */
@media screen and (max-height: 500px) {
    .page-content {
        padding-bottom: var(--spacing-sm);
    }
    
    .config-card {
        margin-bottom: var(--spacing-sm);
    }
    
    .empty-state {
        min-height: 150px;
        padding: var(--spacing-md);
    }
}

/* 超宽屏设备限制 */
@media screen and (min-width: 769px) {
    .app-container {
        max-width: 414px;
        margin: 0 auto;
        box-shadow: var(--shadow-heavy);
        border-radius: 0;
    }
    
    .status-bar-spacer {
        border-radius: 0;
    }
}

/* 打印样式 */
@media print {
    .nav-bar,
    .loading-overlay,
    .toast-container {
        display: none !important;
    }
    
    .page-content {
        padding: 0;
    }
    
    .config-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .config-card-actions {
        display: none;
    }
}

/* 无障碍优化 */
@media (prefers-reduced-transparency: reduce) {
    .nav-bar {
        background-color: var(--background-secondary);
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
    }
    
    .loading-overlay {
        background-color: rgba(0, 0, 0, 0.8);
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
    }
}

/* 焦点可见性增强 */
@media (prefers-reduced-motion: no-preference) {
    .nav-button:focus-visible,
    .config-card-action:focus-visible,
    .ios-button:focus-visible {
        outline: 2px solid var(--ios-blue);
        outline-offset: 2px;
    }
}

/* 强制硬件加速的元素 */
.config-card,
.page,
.nav-bar {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

/* iOS Safari特定修复 */
@supports (-webkit-touch-callout: none) {
    /* 修复iOS Safari的100vh问题 */
    .app-container {
        min-height: calc(var(--vh, 1vh) * 100 - env(safe-area-inset-top, 20px));
    }
    
    /* 修复iOS Safari的滚动问题 */
    .page {
        -webkit-overflow-scrolling: touch;
    }
    
    /* 修复iOS Safari的输入框缩放 */
    input[type="text"],
    input[type="email"],
    input[type="number"],
    textarea {
        font-size: 16px;
    }
}
