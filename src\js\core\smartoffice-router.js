/**
 * @file SmartOffice页面路由管理器
 * @description 实现iOS风格的页面切换和导航管理
 * <AUTHOR> Team
 */

/**
 * @function Router
 * @description 路由管理器构造函数
 * @constructor
 */
function Router() {
    /**
     * @property {Object} routes - 路由配置
     */
    this.routes = {};
    
    /**
     * @property {string} currentRoute - 当前路由
     */
    this.currentRoute = '';
    
    /**
     * @property {Array} history - 路由历史记录
     */
    this.history = [];
    
    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;
    
    /**
     * @property {Object} dom - DOM工具引用
     */
    this.dom = SmartOffice.Utils.DOM;
    
    // 初始化路由
    this.init();
    
    SmartOffice.log('info', 'Router实例创建完成');
}

/**
 * @function Router.prototype.init
 * @description 初始化路由系统
 */
Router.prototype.init = function() {
    // 注册默认路由
    this.registerRoutes();
    
    // 绑定浏览器历史事件
    this.bindHistoryEvents();
    
    SmartOffice.log('info', '路由系统初始化完成');
};

/**
 * @function Router.prototype.registerRoutes
 * @description 注册应用路由
 */
Router.prototype.registerRoutes = function() {
    // 配置列表页面
    this.addRoute('configList', {
        pageId: 'configListPage',
        title: '透视分析',
        showBackButton: false,
        showAddButton: true,
        onEnter: this.onEnterConfigList.bind(this),
        onLeave: this.onLeaveConfigList.bind(this)
    });
    
    // 配置表单页面
    this.addRoute('configForm', {
        pageId: 'configFormPage',
        title: '配置透视表',
        showBackButton: true,
        showAddButton: false,
        onEnter: this.onEnterConfigForm.bind(this),
        onLeave: this.onLeaveConfigForm.bind(this)
    });
    
    SmartOffice.log('info', '路由注册完成');
};

/**
 * @function Router.prototype.addRoute
 * @description 添加路由配置
 * @param {string} name - 路由名称
 * @param {Object} config - 路由配置
 */
Router.prototype.addRoute = function(name, config) {
    this.routes[name] = config;
    SmartOffice.log('info', '路由已添加: ' + name);
};

/**
 * @function Router.prototype.navigate
 * @description 导航到指定路由
 * @param {string} routeName - 路由名称
 * @param {Object} params - 路由参数（可选）
 * @param {boolean} addToHistory - 是否添加到历史记录（默认true）
 */
Router.prototype.navigate = function(routeName, params, addToHistory) {
    if (!this.routes[routeName]) {
        SmartOffice.log('error', '路由不存在: ' + routeName);
        return false;
    }
    
    const route = this.routes[routeName];
    const previousRoute = this.currentRoute;
    
    // 触发页面切换前事件
    this.eventBus.emit(SmartOffice.Events.PAGE_BEFORE_CHANGE, {
        from: previousRoute,
        to: routeName,
        params: params
    });
    
    // 执行当前页面的离开回调
    if (previousRoute && this.routes[previousRoute] && this.routes[previousRoute].onLeave) {
        this.routes[previousRoute].onLeave();
    }
    
    // 执行页面切换动画
    this.performPageTransition(previousRoute, routeName, function() {
        // 更新当前路由
        this.currentRoute = routeName;
        
        // 添加到历史记录
        if (addToHistory !== false) {
            this.addToHistory(routeName, params);
        }
        
        // 执行新页面的进入回调
        if (route.onEnter) {
            route.onEnter(params);
        }
        
        // 更新应用状态
        SmartOffice.State.currentPage = routeName;
        
        // 触发页面切换完成事件
        this.eventBus.emit(SmartOffice.Events.PAGE_AFTER_CHANGE, {
            from: previousRoute,
            to: routeName,
            params: params
        });
        
        SmartOffice.log('info', '导航完成: ' + previousRoute + ' -> ' + routeName);
        
    }.bind(this));
    
    return true;
};

/**
 * @function Router.prototype.performPageTransition
 * @description 执行页面切换动画
 * @param {string} fromRoute - 源路由
 * @param {string} toRoute - 目标路由
 * @param {Function} callback - 完成回调
 */
Router.prototype.performPageTransition = function(fromRoute, toRoute, callback) {
    const fromPage = fromRoute ? this.dom.querySelector('#' + this.routes[fromRoute].pageId) : null;
    const toPage = this.dom.querySelector('#' + this.routes[toRoute].pageId);
    
    if (!toPage) {
        SmartOffice.log('error', '目标页面不存在: ' + this.routes[toRoute].pageId);
        return;
    }
    
    // 确定动画方向
    const isForward = this.isForwardNavigation(fromRoute, toRoute);
    const animationDuration = SmartOffice.Config.ANIMATION_DURATION;
    
    if (fromPage) {
        // 隐藏当前页面
        this.animatePageOut(fromPage, isForward, function() {
            this.dom.addClass(fromPage, 'page-hidden');
            
            // 显示新页面
            this.showNewPage(toPage, isForward, callback);
        }.bind(this));
    } else {
        // 直接显示新页面（首次加载）
        this.showNewPage(toPage, isForward, callback);
    }
};

/**
 * @function Router.prototype.animatePageOut
 * @description 页面退出动画
 * @param {Element} page - 页面元素
 * @param {boolean} isForward - 是否前进导航
 * @param {Function} callback - 完成回调
 */
Router.prototype.animatePageOut = function(page, isForward, callback) {
    const transform = isForward ? 'translateX(-100%)' : 'translateX(100%)';
    
    this.dom.setStyle(page, {
        transform: transform,
        transition: 'transform ' + SmartOffice.Config.ANIMATION_DURATION + 'ms cubic-bezier(0.4, 0.0, 0.2, 1)'
    });
    
    setTimeout(function() {
        this.dom.setStyle(page, {
            transform: '',
            transition: ''
        });
        if (callback) callback();
    }.bind(this), SmartOffice.Config.ANIMATION_DURATION);
};

/**
 * @function Router.prototype.showNewPage
 * @description 显示新页面
 * @param {Element} page - 页面元素
 * @param {boolean} isForward - 是否前进导航
 * @param {Function} callback - 完成回调
 */
Router.prototype.showNewPage = function(page, isForward, callback) {
    const initialTransform = isForward ? 'translateX(100%)' : 'translateX(-100%)';
    
    // 设置初始位置
    this.dom.setStyle(page, {
        transform: initialTransform,
        transition: ''
    });
    
    // 移除隐藏类
    this.dom.removeClass(page, 'page-hidden');
    
    // 强制重绘
    page.offsetHeight;
    
    // 执行进入动画
    this.dom.setStyle(page, {
        transform: 'translateX(0)',
        transition: 'transform ' + SmartOffice.Config.ANIMATION_DURATION + 'ms cubic-bezier(0.4, 0.0, 0.2, 1)'
    });
    
    setTimeout(function() {
        this.dom.setStyle(page, {
            transform: '',
            transition: ''
        });
        if (callback) callback();
    }.bind(this), SmartOffice.Config.ANIMATION_DURATION);
};

/**
 * @function Router.prototype.isForwardNavigation
 * @description 判断是否为前进导航
 * @param {string} fromRoute - 源路由
 * @param {string} toRoute - 目标路由
 * @returns {boolean} 是否前进
 */
Router.prototype.isForwardNavigation = function(fromRoute, toRoute) {
    // 简单的前进判断逻辑
    const routeOrder = ['configList', 'configForm'];
    const fromIndex = routeOrder.indexOf(fromRoute);
    const toIndex = routeOrder.indexOf(toRoute);
    
    return toIndex > fromIndex;
};

/**
 * @function Router.prototype.back
 * @description 返回上一页
 */
Router.prototype.back = function() {
    if (this.history.length > 1) {
        // 移除当前页面
        this.history.pop();
        
        // 获取上一页
        const previousEntry = this.history[this.history.length - 1];
        
        // 导航到上一页（不添加到历史记录）
        this.navigate(previousEntry.route, previousEntry.params, false);
    } else {
        // 默认返回到配置列表
        this.navigate('configList', null, false);
    }
};

/**
 * @function Router.prototype.addToHistory
 * @description 添加到历史记录
 * @param {string} routeName - 路由名称
 * @param {Object} params - 路由参数
 */
Router.prototype.addToHistory = function(routeName, params) {
    this.history.push({
        route: routeName,
        params: params,
        timestamp: Date.now()
    });
    
    // 限制历史记录长度
    if (this.history.length > 10) {
        this.history.shift();
    }
};

/**
 * @function Router.prototype.bindHistoryEvents
 * @description 绑定浏览器历史事件
 */
Router.prototype.bindHistoryEvents = function() {
    const self = this;
    
    window.addEventListener('popstate', function(event) {
        // 处理浏览器后退按钮
        if (event.state && event.state.route) {
            self.navigate(event.state.route, event.state.params, false);
        } else {
            self.navigate('configList', null, false);
        }
    });
};

/**
 * @function Router.prototype.onEnterConfigList
 * @description 进入配置列表页面回调
 */
Router.prototype.onEnterConfigList = function() {
    SmartOffice.log('info', '进入配置列表页面');
    
    // 更新导航栏状态
    this.eventBus.emit(SmartOffice.Events.PAGE_CHANGE, 'configList');
};

/**
 * @function Router.prototype.onLeaveConfigList
 * @description 离开配置列表页面回调
 */
Router.prototype.onLeaveConfigList = function() {
    SmartOffice.log('info', '离开配置列表页面');
};

/**
 * @function Router.prototype.onEnterConfigForm
 * @description 进入配置表单页面回调
 * @param {Object} params - 路由参数
 */
Router.prototype.onEnterConfigForm = function(params) {
    SmartOffice.log('info', '进入配置表单页面', params);
    
    // 更新导航栏状态
    this.eventBus.emit(SmartOffice.Events.PAGE_CHANGE, 'configForm');
    
    // 初始化表单数据
    if (params && params.config) {
        this.eventBus.emit('configForm:loadData', params.config);
    } else {
        this.eventBus.emit('configForm:createNew');
    }
};

/**
 * @function Router.prototype.onLeaveConfigForm
 * @description 离开配置表单页面回调
 */
Router.prototype.onLeaveConfigForm = function() {
    SmartOffice.log('info', '离开配置表单页面');
    
    // 清理表单状态
    this.eventBus.emit('configForm:cleanup');
};

/**
 * @function Router.prototype.getCurrentRoute
 * @description 获取当前路由
 * @returns {string} 当前路由名称
 */
Router.prototype.getCurrentRoute = function() {
    return this.currentRoute;
};

/**
 * @function Router.prototype.getRouteConfig
 * @description 获取路由配置
 * @param {string} routeName - 路由名称
 * @returns {Object|null} 路由配置对象
 */
Router.prototype.getRouteConfig = function(routeName) {
    return this.routes[routeName] || null;
};

// 创建全局路由管理器实例
SmartOffice.Core.Router = new Router();

SmartOffice.log('info', 'SmartOffice路由管理器模块初始化完成');
