# Cline Rules for GoMyHire 移动端快速透视分析项目

## 1. 核心开发原则
-   **移动端优先 (Mobile First)**: 所有UI设计和功能实现都必须首先考虑移动设备的小屏幕和触摸交互。桌面端体验是次要的。
-   **用户核心需求至上**: 严格围绕用户提出的“移动端上传文件后自动根据预设组合生成透视表”的核心场景进行开发。避免早期引入 `readme.md` 中描述的过于复杂或非核心的功能。
-   **性能是关键**: 尤其关注文件解析、数据处理和透视表生成的性能，确保在目标设备（主流手机）上处理5MB文件时有流畅体验。Web Worker 是实现此目标的关键手段。
-   **纯前端，数据本地化**: 严格遵守无后端依赖，所有数据和配置均在用户浏览器本地处理和存储。高度重视数据隐私。
-   **代码规范与注释**:
    *   遵循 `readme.md` 中定义的注释规范，特别是函数级中文注释和关键代码块的标签。
    *   代码组织清晰，模块化。
    *   命名规范：模块名_功能名，层级前缀。

## 2. 技术栈偏好与实践 - **更新为纯原生传统实现**
**基于用户明确要求"不使用需要安装第三方依赖的方案"和"不使用ES6模块方案"，技术选型完全调整为传统原生实现：**
-   **HTML5 + CSS3 + 传统JavaScript**: 作为主要且唯一技术栈，使用传统`<script>`标签加载。
-   **传统JavaScript组织**: 使用全局变量和命名空间模式，避免ES6模块系统。
-   **原生CSS**: 使用纯CSS实现移动端优先的响应式设计。
-   **传统状态管理**: 使用全局对象和DOM事件系统。
-   **自实现数据处理**: 使用原生JavaScript数组方法(map, filter, reduce)实现数据操作和透视表生成。
-   **localStorage**: 用于持久化存储用户配置。
-   **无构建工具**: 直接在浏览器中执行，使用传统`<script src="">`方式加载。
-   **无包管理器**: 完全无外部依赖。

## 3. Memory Bank 更新与维护
-   **任务开始前**: 必须读取所有 Memory Bank 文件，确保完全理解当前项目状态和上下文。
-   **重大变更后**: 在实现重要功能、修改架构或关键技术决策后，及时更新相关的 Memory Bank 文件，特别是 `activeContext.md` 和 `progress.md`。
-   **用户请求 "update memory bank"**: 必须全面审查所有 Memory Bank 文件，即使某些文件看起来不需要修改。重点关注 `activeContext.md` 和 `progress.md`。

## 4. 沟通与确认
-   **开发计划**: 在正式开始编码前，将详细的开发计划（如 `activeContext.md` 中定义的阶段和任务）通过 `plan_mode_respond`（如果处于计划模式）或直接在对话中（如果处于行动模式）呈现给用户，并请求确认。
-   **关键决策**: 对于重要的技术选型变更、架构调整或需求理解上的歧义，及时向用户提问澄清。
-   **阶段性成果**: 在完成主要开发阶段（例如，核心功能原型完成）后，主动向用户展示成果，并寻求反馈。

## 5. 特定功能实现要点
-   **文件上传与解析**:
    *   必须在 Web Worker 中进行，避免UI阻塞。
    *   清晰的上传进度和错误提示。
    *   初步支持 CSV，使用自实现的纯JavaScript CSV解析器。
-   **透视表配置**:
    *   UI应简洁易用，尤其在移动端，使用原生CSS和DOM操作实现。
    *   配置项（行、列、值、筛选）应能动态从数据源字段中选择。
    *   配置需可靠地持久化到 localStorage。
-   **自动透视表生成**:
    *   文件上传成功并解析后，应自动遍历所有用户预设的配置，并更新对应的透视表。
    *   这个过程也应该尽可能在 Web Worker 中完成，或者通过分片处理避免长时间阻塞主线程。
    *   使用原生JavaScript数组方法实现数据分组和聚合计算。
-   **错误处理**:
    *   对文件格式错误、数据结构不匹配、计算错误等情况提供友好和明确的错误提示。

## 6. `readme.md` 的遵循
-   `readme.md` 是重要的需求文档来源，其中描述了许多高级功能和设计细节。
-   当前项目的首要任务是实现用户明确提出的核心移动端场景。
-   在核心场景稳定后，可以逐步参考 `readme.md` 来规划和实现其他功能，但始终要评估其对移动端体验和性能的影响。
-   `readme.md` 中关于UI/UX设计原则（直观性、高效性、灵活性、数据驱动、无缝衔接、前端优先、个性化概览）应贯穿整个开发过程。

*(此文件会随着项目的进展和与用户的交互而不断更新)*
