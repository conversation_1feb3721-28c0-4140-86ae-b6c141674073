/**
 * @file 数据表格组件样式
 * @description 移动端优化的数据表格界面
 * <AUTHOR> Team
 */

/* 数据表格容器 */
.data-table {
    background-color: var(--background-secondary);
    border-radius: var(--radius-medium);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

/* 占位样式 - 后续会完善 */
.data-table-header {
    background-color: var(--background-tertiary);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--separator-opaque);
    font-weight: 600;
    color: var(--text-primary);
}

.data-table-body {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

SmartOffice.log('info', '数据表格样式已加载');
