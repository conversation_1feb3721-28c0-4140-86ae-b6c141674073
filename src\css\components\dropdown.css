/**
 * @file 下拉菜单组件样式
 * @description iOS风格的下拉选择器界面
 * <AUTHOR> Team
 */

/* 下拉菜单容器 */
.dropdown {
    position: relative;
    display: inline-block;
}

/* 占位样式 - 后续会完善 */
.dropdown-button {
    background-color: var(--background-secondary);
    border: 1px solid var(--separator-opaque);
    border-radius: var(--radius-medium);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-body);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--animation-fast) ease;
}

.dropdown-button:hover {
    background-color: var(--background-tertiary);
}

SmartOffice.log('info', '下拉菜单样式已加载');
