# GoMyHire 移动端快速透视分析 - 传统JavaScript项目结构规划

## 目录结构设计

```
GMH-Mobile-Pivot/
├── index.html                 # 主入口文件
├── src/
│   ├── js/
│   │   ├── core/
│   │   │   ├── smartoffice-core.js      # 核心命名空间和基础设施
│   │   │   ├── smartoffice-router.js    # 页面路由管理器
│   │   │   ├── smartoffice-events.js    # 全局事件总线
│   │   │   ├── smartoffice-storage.js   # 本地存储管理
│   │   │   └── smartoffice-app.js       # 应用主控制器
│   │   ├── components/
│   │   │   ├── smartoffice-config-list.js   # 配置列表组件
│   │   │   ├── smartoffice-config-form.js   # 配置表单组件
│   │   │   ├── smartoffice-dropdown.js      # 移动端下拉菜单组件
│   │   │   ├── smartoffice-data-table.js    # 数据表格显示组件
│   │   │   ├── smartoffice-file-upload.js   # 文件上传组件
│   │   │   └── smartoffice-loading.js       # 加载状态组件
│   │   ├── data/
│   │   │   ├── smartoffice-csv-parser.js    # 原生CSV解析器
│   │   │   ├── smartoffice-pivot-engine.js  # 透视表计算引擎
│   │   │   ├── smartoffice-config-manager.js # 配置管理器
│   │   │   └── smartoffice-data-validator.js # 数据验证器
│   │   ├── utils/
│   │   │   ├── smartoffice-helpers.js       # 通用工具函数
│   │   │   ├── smartoffice-dom.js           # DOM操作工具
│   │   │   └── smartoffice-format.js        # 数据格式化工具
│   │   └── workers/
│   │       └── data-processor.js            # Web Worker文件处理
│   ├── css/
│   │   ├── main.css          # 主样式文件
│   │   ├── mobile.css        # 移动端专用样式
│   │   ├── components/       # 组件样式目录
│   │   │   ├── config-list.css
│   │   │   ├── config-form.css
│   │   │   ├── dropdown.css
│   │   │   └── data-table.css
│   │   └── themes/
│   │       ├── light.css     # 浅色主题
│   │       └── dark.css      # 深色主题
│   └── assets/
│       ├── icons/            # 图标文件
│       └── images/           # 图片资源
├── memory-bank/              # 项目文档和规划
└── docs/                     # 用户文档
```

## 文件加载顺序

### index.html中的script标签顺序
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoMyHire 移动端快速透视分析</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="src/css/main.css">
    <link rel="stylesheet" href="src/css/mobile.css">
    <link rel="stylesheet" href="src/css/components/config-list.css">
    <link rel="stylesheet" href="src/css/components/config-form.css">
    <link rel="stylesheet" href="src/css/components/dropdown.css">
    <link rel="stylesheet" href="src/css/components/data-table.css">
</head>
<body>
    <!-- 应用容器 -->
    <div id="app"></div>
    
    <!-- JavaScript文件 - 按依赖顺序加载 -->
    
    <!-- 1. 核心基础设施 -->
    <script src="src/js/core/smartoffice-core.js"></script>
    <script src="src/js/core/smartoffice-events.js"></script>
    <script src="src/js/core/smartoffice-storage.js"></script>
    <script src="src/js/core/smartoffice-router.js"></script>
    
    <!-- 2. 工具函数 -->
    <script src="src/js/utils/smartoffice-helpers.js"></script>
    <script src="src/js/utils/smartoffice-dom.js"></script>
    <script src="src/js/utils/smartoffice-format.js"></script>
    
    <!-- 3. 数据处理模块 -->
    <script src="src/js/data/smartoffice-data-validator.js"></script>
    <script src="src/js/data/smartoffice-csv-parser.js"></script>
    <script src="src/js/data/smartoffice-config-manager.js"></script>
    <script src="src/js/data/smartoffice-pivot-engine.js"></script>
    
    <!-- 4. UI组件 -->
    <script src="src/js/components/smartoffice-loading.js"></script>
    <script src="src/js/components/smartoffice-dropdown.js"></script>
    <script src="src/js/components/smartoffice-file-upload.js"></script>
    <script src="src/js/components/smartoffice-data-table.js"></script>
    <script src="src/js/components/smartoffice-config-form.js"></script>
    <script src="src/js/components/smartoffice-config-list.js"></script>
    
    <!-- 5. 应用主控制器 -->
    <script src="src/js/core/smartoffice-app.js"></script>
    
    <!-- 6. 应用启动 -->
    <script>
        // 应用初始化
        document.addEventListener('DOMContentLoaded', function() {
            SmartOffice.Core.App.init();
        });
    </script>
</body>
</html>
```

## 技术架构设计

### 传统JavaScript模块化系统
- **全局命名空间**: 使用SmartOffice全局对象进行模块管理
- **构造函数模式**: 基于构造函数的组件系统
- **事件驱动**: 自定义事件总线实现模块间通信
- **传统加载**: 使用`<script>`标签按顺序加载文件

### 数据流设计
```
用户操作 → DOM事件 → 事件总线 → 数据处理 → 状态更新 → UI重新渲染
```

### 存储策略
- **配置数据**: localStorage存储透视表配置
- **临时数据**: 内存中处理上传的文件数据
- **缓存机制**: 计算结果的智能缓存

## 命名规范

### 文件命名
- 核心文件: smartoffice-[功能名].js (如 smartoffice-core.js)
- 组件文件: smartoffice-[组件名].js (如 smartoffice-config-list.js)
- 工具文件: smartoffice-[工具名].js (如 smartoffice-helpers.js)
- 样式文件: kebab-case (如 config-list.css)

### 变量命名
- 构造函数: PascalCase (如 ConfigListComponent)
- 实例变量: camelCase (如 configListInstance)
- 常量: UPPER_SNAKE_CASE (如 MAX_FILE_SIZE)
- CSS类名: kebab-case (如 .config-card)

### 函数命名
- 初始化函数: init + 功能名 (如 initConfigList)
- 事件处理: handle + 事件名 (如 handleFileUpload)
- 渲染函数: render + 组件名 (如 renderConfigCard)

## 开发环境配置

### 必需工具
- **开发服务器**: Live Server (VS Code插件)
- **代码编辑器**: VS Code
- **浏览器**: Chrome DevTools (主要调试工具)

### 推荐VS Code插件
- Live Server
- HTML CSS Support
- JavaScript (ES6) code snippets
- Prettier - Code formatter

## 性能考虑

### 文件加载优化
- 按需加载模块
- CSS文件分离
- 图片资源优化

### 内存管理
- 及时清理大数据对象
- Web Worker处理重计算
- 避免内存泄漏

### 移动端优化
- 触摸事件优化
- 视口配置
- 响应式图片

## 兼容性策略

### 浏览器支持
- 现代移动浏览器 (iOS Safari 12+, Chrome Mobile 70+)
- 使用传统JavaScript语法确保兼容性
- 避免使用最新ES特性

### 降级方案
- localStorage不可用时的内存存储
- Web Workers不支持时的主线程处理
- 触摸事件不支持时的鼠标事件

## 调试策略

### 开发模式
- 详细的console.log输出
- 错误边界处理
- 性能监控点

### 生产模式
- 最小化日志输出
- 用户友好的错误提示
- 性能优化
