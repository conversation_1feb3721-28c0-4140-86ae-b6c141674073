/**
 * @file SmartOffice文件上传组件
 * @description 移动端优化的文件上传组件，支持CSV文件上传和解析
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function FileUploadComponent
 * @description 文件上传组件构造函数
 * @constructor
 * @param {Object} options - 配置选项
 * @param {string} options.containerId - 容器元素ID
 * @param {Array} options.acceptedTypes - 接受的文件类型
 * @param {number} options.maxSize - 最大文件大小（字节）
 * @param {Function} options.onUpload - 文件上传回调
 * @param {Function} options.onProgress - 上传进度回调
 * @param {Function} options.onError - 错误回调
 */
function FileUploadComponent(options) {
    // 默认配置
    this.options = SmartOffice.Utils.Helpers.extend({
        containerId: 'fileUploadContainer',
        acceptedTypes: ['csv', 'txt'],
        maxSize: SmartOffice.Config.MAX_FILE_SIZE, // 5MB
        onUpload: null,
        onProgress: null,
        onError: null
    }, options || {});

    // 组件状态
    this.isUploading = false;
    this.currentFile = null;
    this.uploadProgress = 0;

    // DOM元素引用
    this.containerElement = null;
    this.fileInputElement = null;
    this.dropZoneElement = null;
    this.progressElement = null;
    this.statusElement = null;

    // 依赖注入
    this.eventBus = SmartOffice.Core.EventBus;
    this.dom = SmartOffice.Utils.DOM;
    this.helpers = SmartOffice.Utils.Helpers;
    this.dataValidator = new SmartOffice.Data.DataValidator();
    this.csvParser = new SmartOffice.Data.CSVParser();

    SmartOffice.log('info', 'FileUploadComponent初始化完成');
}

/**
 * @function FileUploadComponent.prototype.init
 * @description 初始化文件上传组件
 * @returns {boolean} 初始化是否成功
 */
FileUploadComponent.prototype.init = function() {
    try {
        // 获取容器元素
        this.containerElement = document.getElementById(this.options.containerId);
        if (!this.containerElement) {
            throw new Error('找不到文件上传容器元素: ' + this.options.containerId);
        }

        // 渲染组件界面
        this.render();

        // 绑定事件监听器
        this.bindEvents();

        SmartOffice.log('info', '文件上传组件初始化成功');
        return true;
    } catch (error) {
        SmartOffice.log('error', '文件上传组件初始化失败:', error);
        return false;
    }
};

/**
 * @function FileUploadComponent.prototype.render
 * @description 渲染文件上传界面
 */
FileUploadComponent.prototype.render = function() {
    const html = `
        <div class="so-file-upload">
            <!-- 文件拖拽区域 -->
            <div class="so-upload-dropzone" id="uploadDropzone">
                <div class="so-upload-icon">
                    <svg viewBox="0 0 24 24" class="upload-icon">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        <path d="M12,11L16,15H13V19H11V15H8L12,11Z"/>
                    </svg>
                </div>
                <h3 class="so-upload-title">选择CSV文件</h3>
                <p class="so-upload-description">
                    点击选择文件或拖拽文件到此区域<br>
                    支持CSV格式，最大${this.formatFileSize(this.options.maxSize)}
                </p>
                <button class="so-upload-button" type="button" id="uploadButton">
                    选择文件
                </button>
            </div>

            <!-- 隐藏的文件输入 -->
            <input type="file"
                   id="fileInput"
                   class="so-file-input"
                   accept=".csv,.txt"
                   style="display: none;">

            <!-- 上传进度 -->
            <div class="so-upload-progress" id="uploadProgress" style="display: none;">
                <div class="so-progress-info">
                    <span class="so-progress-filename" id="progressFilename"></span>
                    <span class="so-progress-percentage" id="progressPercentage">0%</span>
                </div>
                <div class="so-progress-bar">
                    <div class="so-progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
                <div class="so-progress-status" id="progressStatus">准备上传...</div>
            </div>

            <!-- 上传结果 -->
            <div class="so-upload-result" id="uploadResult" style="display: none;">
                <div class="so-result-icon" id="resultIcon"></div>
                <div class="so-result-message" id="resultMessage"></div>
                <button class="so-result-action" id="resultAction" style="display: none;">
                    重新选择文件
                </button>
            </div>
        </div>
    `;

    this.containerElement.innerHTML = html;

    // 获取DOM元素引用
    this.fileInputElement = document.getElementById('fileInput');
    this.dropZoneElement = document.getElementById('uploadDropzone');
    this.progressElement = document.getElementById('uploadProgress');
    this.statusElement = document.getElementById('progressStatus');
};

/**
 * @function FileUploadComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
FileUploadComponent.prototype.bindEvents = function() {
    const self = this;

    // 文件选择按钮点击事件
    const uploadButton = document.getElementById('uploadButton');
    if (uploadButton) {
        uploadButton.addEventListener('click', function() {
            self.fileInputElement.click();
        });
    }

    // 文件输入变化事件
    if (this.fileInputElement) {
        this.fileInputElement.addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                self.handleFileSelect(file);
            }
        });
    }

    // 拖拽事件处理
    if (this.dropZoneElement) {
        // 防止默认拖拽行为
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            this.dropZoneElement.addEventListener(eventName, function(e) {
                e.preventDefault();
                e.stopPropagation();
            });
        });

        // 拖拽进入和悬停
        ['dragenter', 'dragover'].forEach(eventName => {
            this.dropZoneElement.addEventListener(eventName, function() {
                self.dom.addClass(self.dropZoneElement, 'so-dragover');
            });
        });

        // 拖拽离开
        this.dropZoneElement.addEventListener('dragleave', function() {
            self.dom.removeClass(self.dropZoneElement, 'so-dragover');
        });

        // 文件拖拽放下
        this.dropZoneElement.addEventListener('drop', function(e) {
            self.dom.removeClass(self.dropZoneElement, 'so-dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                self.handleFileSelect(files[0]);
            }
        });
    }

    // 重新选择文件按钮
    const resultAction = document.getElementById('resultAction');
    if (resultAction) {
        resultAction.addEventListener('click', function() {
            self.resetUpload();
        });
    }
};

/**
 * @function FileUploadComponent.prototype.handleFileSelect
 * @description 处理文件选择
 * @param {File} file - 选择的文件对象
 */
FileUploadComponent.prototype.handleFileSelect = function(file) {
    try {
        // 验证文件
        const validationResult = this.validateFile(file);
        if (!validationResult.isValid) {
            this.showError(validationResult.message);
            return;
        }

        // 保存当前文件
        this.currentFile = file;

        // 显示上传进度界面
        this.showProgress();

        // 开始文件处理
        this.processFile(file);

    } catch (error) {
        SmartOffice.log('error', '文件选择处理失败:', error);
        this.showError('文件处理失败，请重试');
    }
};

/**
 * @function FileUploadComponent.prototype.validateFile
 * @description 验证文件是否符合要求
 * @param {File} file - 要验证的文件
 * @returns {Object} 验证结果 {isValid: boolean, message: string}
 */
FileUploadComponent.prototype.validateFile = function(file) {
    // 检查文件是否存在
    if (!file) {
        return { isValid: false, message: '请选择一个文件' };
    }

    // 检查文件大小
    if (file.size > this.options.maxSize) {
        return {
            isValid: false,
            message: `文件大小超过限制，最大允许${this.formatFileSize(this.options.maxSize)}`
        };
    }

    // 检查文件类型
    const fileExtension = this.getFileExtension(file.name);
    if (!this.options.acceptedTypes.includes(fileExtension)) {
        return {
            isValid: false,
            message: `不支持的文件类型，请选择${this.options.acceptedTypes.join('、')}格式的文件`
        };
    }

    // 检查文件名
    if (file.name.length > 255) {
        return { isValid: false, message: '文件名过长' };
    }

    return { isValid: true, message: '文件验证通过' };
};

/**
 * @function FileUploadComponent.prototype.processFile
 * @description 处理文件上传和解析
 * @param {File} file - 要处理的文件
 */
FileUploadComponent.prototype.processFile = function(file) {
    const self = this;

    // 设置上传状态
    this.isUploading = true;
    this.updateProgress(0, '开始读取文件...');

    // 触发文件上传开始事件
    this.eventBus.emit(SmartOffice.Events.FILE_UPLOAD_START, {
        file: file,
        size: file.size,
        name: file.name
    });

    // 使用FileReader读取文件
    const reader = new FileReader();

    reader.onload = function(event) {
        try {
            const fileContent = event.target.result;
            self.updateProgress(50, '文件读取完成，开始解析...');

            // 解析CSV文件
            self.parseCSVContent(fileContent, file);

        } catch (error) {
            SmartOffice.log('error', '文件读取失败:', error);
            self.showError('文件读取失败，请检查文件格式');
            self.isUploading = false;
        }
    };

    reader.onerror = function() {
        SmartOffice.log('error', '文件读取出错');
        self.showError('文件读取出错，请重试');
        self.isUploading = false;
    };

    reader.onprogress = function(event) {
        if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 50); // 读取占50%进度
            self.updateProgress(progress, '正在读取文件...');
        }
    };

    // 开始读取文件
    reader.readAsText(file, 'UTF-8');
};

/**
 * @function FileUploadComponent.prototype.parseCSVContent
 * @description 解析CSV文件内容
 * @param {string} content - 文件内容
 * @param {File} file - 原始文件对象
 */
FileUploadComponent.prototype.parseCSVContent = function(content, file) {
    const self = this;

    try {
        this.updateProgress(60, '正在解析CSV数据...');

        // 使用CSV解析器解析内容
        this.csvParser.parse(content, {
            onProgress: function(progress) {
                const totalProgress = 50 + Math.round(progress * 40); // 解析占40%进度
                self.updateProgress(totalProgress, '正在解析数据...');
            },
            onComplete: function(result) {
                self.handleParseComplete(result, file);
            },
            onError: function(error) {
                self.handleParseError(error);
            }
        });

    } catch (error) {
        SmartOffice.log('error', 'CSV解析失败:', error);
        this.showError('CSV文件解析失败，请检查文件格式');
        this.isUploading = false;
    }
};

/**
 * @function FileUploadComponent.prototype.handleParseComplete
 * @description 处理解析完成
 * @param {Object} result - 解析结果
 * @param {File} file - 原始文件对象
 */
FileUploadComponent.prototype.handleParseComplete = function(result, file) {
    try {
        this.updateProgress(90, '数据验证中...');

        // 验证解析结果
        const validationResult = this.dataValidator.validateParsedData(result);
        if (!validationResult.isValid) {
            this.showError('数据验证失败: ' + validationResult.message);
            return;
        }

        this.updateProgress(100, '上传完成！');

        // 显示成功结果
        this.showSuccess(result, file);

        // 触发上传完成事件
        this.eventBus.emit(SmartOffice.Events.FILE_UPLOAD_COMPLETE, {
            file: file,
            data: result,
            timestamp: new Date().toISOString()
        });

        // 调用回调函数
        if (this.options.onUpload) {
            this.options.onUpload(result, file);
        }

        SmartOffice.log('info', '文件上传和解析完成:', file.name);

    } catch (error) {
        SmartOffice.log('error', '解析完成处理失败:', error);
        this.showError('数据处理失败，请重试');
    } finally {
        this.isUploading = false;
    }
};

/**
 * @function FileUploadComponent.prototype.handleParseError
 * @description 处理解析错误
 * @param {Error} error - 错误对象
 */
FileUploadComponent.prototype.handleParseError = function(error) {
    SmartOffice.log('error', 'CSV解析错误:', error);
    this.showError('文件解析失败: ' + (error.message || '未知错误'));
    this.isUploading = false;
};

/**
 * @function FileUploadComponent.prototype.updateProgress
 * @description 更新上传进度
 * @param {number} percentage - 进度百分比 (0-100)
 * @param {string} status - 状态文本
 */
FileUploadComponent.prototype.updateProgress = function(percentage, status) {
    this.uploadProgress = percentage;

    // 更新进度条
    const progressFill = document.getElementById('progressFill');
    const progressPercentage = document.getElementById('progressPercentage');
    const progressStatus = document.getElementById('progressStatus');

    if (progressFill) {
        progressFill.style.width = percentage + '%';
    }

    if (progressPercentage) {
        progressPercentage.textContent = Math.round(percentage) + '%';
    }

    if (progressStatus) {
        progressStatus.textContent = status || '';
    }

    // 触发进度事件
    this.eventBus.emit(SmartOffice.Events.FILE_UPLOAD_PROGRESS, {
        percentage: percentage,
        status: status
    });

    // 调用进度回调
    if (this.options.onProgress) {
        this.options.onProgress(percentage, status);
    }
};

/**
 * @function FileUploadComponent.prototype.showProgress
 * @description 显示上传进度界面
 */
FileUploadComponent.prototype.showProgress = function() {
    // 隐藏拖拽区域
    this.dom.setStyle(this.dropZoneElement, { display: 'none' });

    // 显示进度区域
    this.dom.setStyle(this.progressElement, { display: 'block' });

    // 更新文件名显示
    const progressFilename = document.getElementById('progressFilename');
    if (progressFilename && this.currentFile) {
        progressFilename.textContent = this.currentFile.name;
    }

    // 重置进度
    this.updateProgress(0, '准备上传...');
};

/**
 * @function FileUploadComponent.prototype.showSuccess
 * @description 显示上传成功界面
 * @param {Object} result - 解析结果
 * @param {File} file - 文件对象
 */
FileUploadComponent.prototype.showSuccess = function(result, file) {
    // 隐藏进度区域
    this.dom.setStyle(this.progressElement, { display: 'none' });

    // 显示结果区域
    const uploadResult = document.getElementById('uploadResult');
    const resultIcon = document.getElementById('resultIcon');
    const resultMessage = document.getElementById('resultMessage');
    const resultAction = document.getElementById('resultAction');

    if (uploadResult) {
        this.dom.setStyle(uploadResult, { display: 'block' });
    }

    if (resultIcon) {
        resultIcon.innerHTML = `
            <svg viewBox="0 0 24 24" class="success-icon">
                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M11,16.5L18,9.5L16.59,8.09L11,13.67L7.91,10.59L6.5,12L11,16.5Z"/>
            </svg>
        `;
        this.dom.addClass(resultIcon, 'so-success');
    }

    if (resultMessage) {
        const rowCount = result.data ? result.data.length : 0;
        const columnCount = result.headers ? result.headers.length : 0;
        resultMessage.innerHTML = `
            <h3>文件上传成功！</h3>
            <p>已解析 <strong>${rowCount}</strong> 行数据，包含 <strong>${columnCount}</strong> 个字段</p>
            <p class="file-info">文件：${file.name} (${this.formatFileSize(file.size)})</p>
        `;
    }

    if (resultAction) {
        resultAction.textContent = '重新选择文件';
        this.dom.setStyle(resultAction, { display: 'inline-block' });
    }
};

/**
 * @function FileUploadComponent.prototype.showError
 * @description 显示错误信息
 * @param {string} message - 错误消息
 */
FileUploadComponent.prototype.showError = function(message) {
    // 隐藏进度区域
    if (this.progressElement) {
        this.dom.setStyle(this.progressElement, { display: 'none' });
    }

    // 显示结果区域
    const uploadResult = document.getElementById('uploadResult');
    const resultIcon = document.getElementById('resultIcon');
    const resultMessage = document.getElementById('resultMessage');
    const resultAction = document.getElementById('resultAction');

    if (uploadResult) {
        this.dom.setStyle(uploadResult, { display: 'block' });
    }

    if (resultIcon) {
        resultIcon.innerHTML = `
            <svg viewBox="0 0 24 24" class="error-icon">
                <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,7A1,1 0 0,0 11,8V12A1,1 0 0,0 12,13A1,1 0 0,0 13,12V8A1,1 0 0,0 12,7M12,17.5A1.5,1.5 0 0,1 10.5,16A1.5,1.5 0 0,1 12,14.5A1.5,1.5 0 0,1 13.5,16A1.5,1.5 0 0,1 12,17.5Z"/>
            </svg>
        `;
        this.dom.addClass(resultIcon, 'so-error');
    }

    if (resultMessage) {
        resultMessage.innerHTML = `
            <h3>上传失败</h3>
            <p>${message}</p>
        `;
    }

    if (resultAction) {
        resultAction.textContent = '重新选择文件';
        this.dom.setStyle(resultAction, { display: 'inline-block' });
    }

    // 触发错误事件
    this.eventBus.emit(SmartOffice.Events.FILE_UPLOAD_ERROR, {
        message: message,
        timestamp: new Date().toISOString()
    });

    // 调用错误回调
    if (this.options.onError) {
        this.options.onError(message);
    }
};

/**
 * @function FileUploadComponent.prototype.resetUpload
 * @description 重置上传状态，返回初始界面
 */
FileUploadComponent.prototype.resetUpload = function() {
    // 重置状态
    this.isUploading = false;
    this.currentFile = null;
    this.uploadProgress = 0;

    // 清空文件输入
    if (this.fileInputElement) {
        this.fileInputElement.value = '';
    }

    // 隐藏进度和结果区域
    const uploadProgress = document.getElementById('uploadProgress');
    const uploadResult = document.getElementById('uploadResult');

    if (uploadProgress) {
        this.dom.setStyle(uploadProgress, { display: 'none' });
    }

    if (uploadResult) {
        this.dom.setStyle(uploadResult, { display: 'none' });
    }

    // 显示拖拽区域
    if (this.dropZoneElement) {
        this.dom.setStyle(this.dropZoneElement, { display: 'block' });
        this.dom.removeClass(this.dropZoneElement, 'so-dragover');
    }

    SmartOffice.log('info', '文件上传组件已重置');
};

/**
 * @function FileUploadComponent.prototype.getFileExtension
 * @description 获取文件扩展名
 * @param {string} filename - 文件名
 * @returns {string} 文件扩展名（小写）
 */
FileUploadComponent.prototype.getFileExtension = function(filename) {
    if (!filename || typeof filename !== 'string') {
        return '';
    }

    const lastDotIndex = filename.lastIndexOf('.');
    if (lastDotIndex === -1 || lastDotIndex === filename.length - 1) {
        return '';
    }

    return filename.substring(lastDotIndex + 1).toLowerCase();
};

/**
 * @function FileUploadComponent.prototype.formatFileSize
 * @description 格式化文件大小显示
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
FileUploadComponent.prototype.formatFileSize = function(bytes) {
    if (!bytes || bytes === 0) {
        return '0 B';
    }

    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }

    return Math.round(size * 100) / 100 + ' ' + units[unitIndex];
};

/**
 * @function FileUploadComponent.prototype.destroy
 * @description 销毁组件，清理资源
 */
FileUploadComponent.prototype.destroy = function() {
    try {
        // 取消正在进行的上传
        if (this.isUploading) {
            this.isUploading = false;
        }

        // 清理DOM事件监听器
        if (this.fileInputElement) {
            this.fileInputElement.removeEventListener('change', this.handleFileSelect);
        }

        // 清理拖拽事件
        if (this.dropZoneElement) {
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                this.dropZoneElement.removeEventListener(eventName, function() {});
            });
        }

        // 清空容器
        if (this.containerElement) {
            this.containerElement.innerHTML = '';
        }

        // 清理引用
        this.containerElement = null;
        this.fileInputElement = null;
        this.dropZoneElement = null;
        this.progressElement = null;
        this.statusElement = null;
        this.currentFile = null;

        SmartOffice.log('info', '文件上传组件已销毁');

    } catch (error) {
        SmartOffice.log('error', '文件上传组件销毁失败:', error);
    }
};

/**
 * @function FileUploadComponent.prototype.getCurrentFile
 * @description 获取当前选择的文件
 * @returns {File|null} 当前文件对象
 */
FileUploadComponent.prototype.getCurrentFile = function() {
    return this.currentFile;
};

/**
 * @function FileUploadComponent.prototype.getUploadProgress
 * @description 获取当前上传进度
 * @returns {number} 上传进度百分比 (0-100)
 */
FileUploadComponent.prototype.getUploadProgress = function() {
    return this.uploadProgress;
};

/**
 * @function FileUploadComponent.prototype.isUploading
 * @description 检查是否正在上传
 * @returns {boolean} 是否正在上传
 */
FileUploadComponent.prototype.isCurrentlyUploading = function() {
    return this.isUploading;
};

// 注册到全局命名空间
SmartOffice.Components.FileUpload = FileUploadComponent;

SmartOffice.log('info', 'SmartOffice文件上传组件模块初始化完成');
