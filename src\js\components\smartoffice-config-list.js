/**
 * @file SmartOffice配置列表组件
 * @description iOS风格的透视表配置列表界面组件
 * <AUTHOR> Team
 */

/**
 * @function ConfigListComponent
 * @description 配置列表组件构造函数
 * @param {Element} container - 容器元素
 * @constructor
 */
function ConfigListComponent(container) {
    /**
     * @property {Element} container - 组件容器
     */
    this.container = container;
    
    /**
     * @property {Array} configs - 配置列表数据
     */
    this.configs = [];
    
    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;
    
    /**
     * @property {Object} storage - 存储管理器引用
     */
    this.storage = SmartOffice.Core.Storage;
    
    /**
     * @property {Object} helpers - 工具函数引用
     */
    this.helpers = SmartOffice.Utils.Helpers;
    
    /**
     * @property {Object} dom - DOM工具引用
     */
    this.dom = SmartOffice.Utils.DOM;
    
    // 初始化组件
    this.init();
    
    SmartOffice.log('info', 'ConfigListComponent实例创建完成');
}

/**
 * @function ConfigListComponent.prototype.init
 * @description 初始化组件
 */
ConfigListComponent.prototype.init = function() {
    // 加载配置数据
    this.loadConfigs();
    
    // 渲染界面
    this.render();
    
    // 绑定事件
    this.bindEvents();
    
    SmartOffice.log('info', '配置列表组件初始化完成');
};

/**
 * @function ConfigListComponent.prototype.loadConfigs
 * @description 从存储中加载配置数据
 */
ConfigListComponent.prototype.loadConfigs = function() {
    try {
        this.configs = this.storage.get(SmartOffice.StorageKeys.CONFIGS, []);

        // 如果没有配置数据，创建一些演示数据
        if (this.configs.length === 0) {
            this.configs = this.createDemoConfigs();
            this.saveConfigs();
        }

        SmartOffice.log('info', '已加载配置数据，共' + this.configs.length + '个配置');
    } catch (error) {
        SmartOffice.log('error', '加载配置数据失败:', error);
        this.configs = [];
    }
};

/**
 * @function ConfigListComponent.prototype.createDemoConfigs
 * @description 创建演示配置数据
 * @returns {Array} 演示配置数组
 */
ConfigListComponent.prototype.createDemoConfigs = function() {
    const now = new Date().toISOString();

    return [
        {
            id: this.helpers.generateId('config'),
            name: '销售数据透视表',
            description: '按地区和产品分析销售数据',
            rowFields: ['地区', '销售员'],
            columnFields: ['产品类别'],
            valueFields: ['销售额', '数量'],
            filterFields: ['时间'],
            aggregationType: 'sum',
            createdAt: now,
            updatedAt: now
        },
        {
            id: this.helpers.generateId('config'),
            name: '客户分析报表',
            description: '客户购买行为分析',
            rowFields: ['客户类型'],
            columnFields: ['月份'],
            valueFields: ['订单金额'],
            filterFields: ['客户等级'],
            aggregationType: 'avg',
            createdAt: now,
            updatedAt: now
        }
    ];
};

/**
 * @function ConfigListComponent.prototype.saveConfigs
 * @description 保存配置数据到存储
 */
ConfigListComponent.prototype.saveConfigs = function() {
    try {
        this.storage.set(SmartOffice.StorageKeys.CONFIGS, this.configs);
        SmartOffice.log('info', '配置数据已保存');
        return true;
    } catch (error) {
        SmartOffice.log('error', '保存配置数据失败:', error);
        return false;
    }
};

/**
 * @function ConfigListComponent.prototype.render
 * @description 渲染配置列表界面
 */
ConfigListComponent.prototype.render = function() {
    if (!this.container) {
        SmartOffice.log('error', '配置列表容器不存在');
        return;
    }
    
    // 清空容器
    this.container.innerHTML = '';
    
    if (this.configs.length === 0) {
        this.renderEmptyState();
        return;
    }
    
    // 渲染配置卡片
    for (let i = 0; i < this.configs.length; i++) {
        const configCard = this.createConfigCard(this.configs[i]);
        this.container.appendChild(configCard);
    }
    
    SmartOffice.log('info', '配置列表渲染完成，共' + this.configs.length + '个配置');
};

/**
 * @function ConfigListComponent.prototype.renderEmptyState
 * @description 渲染空状态
 */
ConfigListComponent.prototype.renderEmptyState = function() {
    const emptyState = this.dom.querySelector('#emptyState');
    if (emptyState) {
        this.dom.show(emptyState);
    }
};

/**
 * @function ConfigListComponent.prototype.createConfigCard
 * @description 创建配置卡片元素
 * @param {Object} config - 配置数据
 * @returns {Element} 配置卡片元素
 */
ConfigListComponent.prototype.createConfigCard = function(config) {
    const card = this.dom.createElement('div', {
        className: 'config-card touchable',
        dataset: { configId: config.id }
    });
    
    // 添加触摸反馈
    this.dom.addTouchFeedback(card, 'light');
    
    // 创建卡片头部
    const header = this.createCardHeader(config);
    card.appendChild(header);
    
    // 创建卡片内容
    const content = this.createCardContent(config);
    card.appendChild(content);
    
    // 创建卡片底部
    const footer = this.createCardFooter(config);
    card.appendChild(footer);
    
    return card;
};

/**
 * @function ConfigListComponent.prototype.createCardHeader
 * @description 创建卡片头部
 * @param {Object} config - 配置数据
 * @returns {Element} 头部元素
 */
ConfigListComponent.prototype.createCardHeader = function(config) {
    const header = this.dom.createElement('div', { className: 'config-card-header' });
    
    // 标题区域
    const titleSection = this.dom.createElement('div', { className: 'config-card-title-section' });
    
    const title = this.dom.createElement('h3', { className: 'config-card-title' }, config.name);
    const subtitle = this.dom.createElement('p', { className: 'config-card-subtitle' }, config.description || '暂无描述');
    
    titleSection.appendChild(title);
    titleSection.appendChild(subtitle);
    
    // 操作按钮区域
    const actions = this.dom.createElement('div', { className: 'config-card-actions' });
    
    // 编辑按钮
    const editBtn = this.dom.createElement('button', {
        className: 'config-card-action edit',
        dataset: { action: 'edit', configId: config.id }
    });
    editBtn.innerHTML = '<svg class="config-card-action-icon" viewBox="0 0 24 24"><path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/></svg>';
    
    // 删除按钮
    const deleteBtn = this.dom.createElement('button', {
        className: 'config-card-action delete',
        dataset: { action: 'delete', configId: config.id }
    });
    deleteBtn.innerHTML = '<svg class="config-card-action-icon" viewBox="0 0 24 24"><path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/></svg>';
    
    actions.appendChild(editBtn);
    actions.appendChild(deleteBtn);
    
    header.appendChild(titleSection);
    header.appendChild(actions);
    
    return header;
};

/**
 * @function ConfigListComponent.prototype.createCardContent
 * @description 创建卡片内容
 * @param {Object} config - 配置数据
 * @returns {Element} 内容元素
 */
ConfigListComponent.prototype.createCardContent = function(config) {
    const content = this.dom.createElement('div', { className: 'config-card-content' });
    
    // 元数据信息
    const meta = this.dom.createElement('div', { className: 'config-card-meta' });
    
    // 行字段
    if (config.rowFields && config.rowFields.length > 0) {
        const rowMeta = this.createMetaItem('行字段', config.rowFields.join(', '));
        meta.appendChild(rowMeta);
    }
    
    // 列字段
    if (config.columnFields && config.columnFields.length > 0) {
        const colMeta = this.createMetaItem('列字段', config.columnFields.join(', '));
        meta.appendChild(colMeta);
    }
    
    // 值字段
    if (config.valueFields && config.valueFields.length > 0) {
        const valueMeta = this.createMetaItem('值字段', config.valueFields.join(', '));
        meta.appendChild(valueMeta);
    }
    
    content.appendChild(meta);
    
    // 字段标签
    const fields = this.dom.createElement('div', { className: 'config-card-fields' });
    
    // 添加字段标签
    this.addFieldTags(fields, config.rowFields, 'row', '行');
    this.addFieldTags(fields, config.columnFields, 'column', '列');
    this.addFieldTags(fields, config.valueFields, 'value', '值');
    this.addFieldTags(fields, config.filterFields, 'filter', '筛选');
    
    content.appendChild(fields);
    
    return content;
};

/**
 * @function ConfigListComponent.prototype.createMetaItem
 * @description 创建元数据项
 * @param {string} label - 标签
 * @param {string} value - 值
 * @returns {Element} 元数据项元素
 */
ConfigListComponent.prototype.createMetaItem = function(label, value) {
    const item = this.dom.createElement('div', { className: 'config-card-meta-item' });
    
    const labelEl = this.dom.createElement('span', { className: 'config-card-meta-label' }, label);
    const valueEl = this.dom.createElement('span', { className: 'config-card-meta-value' }, value);
    
    item.appendChild(labelEl);
    item.appendChild(valueEl);
    
    return item;
};

/**
 * @function ConfigListComponent.prototype.addFieldTags
 * @description 添加字段标签
 * @param {Element} container - 容器元素
 * @param {Array} fields - 字段数组
 * @param {string} type - 字段类型
 * @param {string} prefix - 前缀
 */
ConfigListComponent.prototype.addFieldTags = function(container, fields, type, prefix) {
    if (!fields || fields.length === 0) return;
    
    for (let i = 0; i < fields.length; i++) {
        const tag = this.dom.createElement('span', {
            className: 'config-field-tag ' + type
        }, prefix + ': ' + fields[i]);
        
        container.appendChild(tag);
    }
};

/**
 * @function ConfigListComponent.prototype.createCardFooter
 * @description 创建卡片底部
 * @param {Object} config - 配置数据
 * @returns {Element} 底部元素
 */
ConfigListComponent.prototype.createCardFooter = function(config) {
    const footer = this.dom.createElement('div', { className: 'config-card-footer' });
    
    // 时间戳
    const timestamp = this.dom.createElement('div', { className: 'config-card-timestamp' });
    timestamp.innerHTML = '<svg class="config-card-timestamp-icon" viewBox="0 0 24 24"><path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/><path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/></svg>';
    
    const timeText = this.helpers.formatDate(config.updatedAt || config.createdAt, 'relative');
    timestamp.appendChild(document.createTextNode(timeText));
    
    // 状态
    const status = this.dom.createElement('div', { className: 'config-card-status' });
    const statusDot = this.dom.createElement('div', { className: 'config-card-status-dot ready' });
    const statusText = this.dom.createElement('span', {}, '就绪');
    
    status.appendChild(statusDot);
    status.appendChild(statusText);
    
    footer.appendChild(timestamp);
    footer.appendChild(status);
    
    return footer;
};

/**
 * @function ConfigListComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
ConfigListComponent.prototype.bindEvents = function() {
    const self = this;
    
    // 监听配置相关事件
    this.eventBus.on(SmartOffice.Events.CONFIG_CREATE, function(config) {
        self.addConfig(config);
    });
    
    this.eventBus.on(SmartOffice.Events.CONFIG_UPDATE, function(config) {
        self.updateConfig(config);
    });
    
    this.eventBus.on(SmartOffice.Events.CONFIG_DELETE, function(configId) {
        self.deleteConfig(configId);
    });
    
    // 绑定容器点击事件
    if (this.container) {
        this.container.addEventListener('click', function(event) {
            self.handleClick(event);
        });
    }
    
    SmartOffice.log('info', '配置列表事件绑定完成');
};

/**
 * @function ConfigListComponent.prototype.handleClick
 * @description 处理点击事件
 * @param {Event} event - 点击事件
 */
ConfigListComponent.prototype.handleClick = function(event) {
    const target = event.target.closest('[data-action]');
    const configCard = event.target.closest('.config-card');

    if (target) {
        // 处理操作按钮点击
        event.preventDefault();
        event.stopPropagation();

        const action = target.dataset.action;
        const configId = target.dataset.configId;

        switch (action) {
            case 'edit':
                this.editConfig(configId);
                break;
            case 'delete':
                this.confirmDeleteConfig(configId);
                break;
        }
    } else if (configCard) {
        // 处理配置卡片点击
        event.preventDefault();
        const configId = configCard.dataset.configId;
        if (configId) {
            this.editConfig(configId);
        }
    }
};

/**
 * @function ConfigListComponent.prototype.editConfig
 * @description 编辑配置
 * @param {string} configId - 配置ID
 */
ConfigListComponent.prototype.editConfig = function(configId) {
    const config = this.findConfigById(configId);
    if (!config) {
        SmartOffice.log('error', '找不到配置: ' + configId);
        return;
    }
    
    // 触发编辑事件
    this.eventBus.emit(SmartOffice.Events.CONFIG_SELECT, config);
    
    SmartOffice.log('info', '开始编辑配置: ' + config.name);
};

/**
 * @function ConfigListComponent.prototype.confirmDeleteConfig
 * @description 确认删除配置
 * @param {string} configId - 配置ID
 */
ConfigListComponent.prototype.confirmDeleteConfig = function(configId) {
    const config = this.findConfigById(configId);
    if (!config) return;
    
    // 简单的确认对话框（后续可以替换为iOS风格的ActionSheet）
    if (confirm('确定要删除配置 "' + config.name + '" 吗？')) {
        this.deleteConfig(configId);
    }
};

/**
 * @function ConfigListComponent.prototype.findConfigById
 * @description 根据ID查找配置
 * @param {string} configId - 配置ID
 * @returns {Object|null} 配置对象或null
 */
ConfigListComponent.prototype.findConfigById = function(configId) {
    for (let i = 0; i < this.configs.length; i++) {
        if (this.configs[i].id === configId) {
            return this.configs[i];
        }
    }
    return null;
};

/**
 * @function ConfigListComponent.prototype.addConfig
 * @description 添加新配置
 * @param {Object} config - 配置对象
 */
ConfigListComponent.prototype.addConfig = function(config) {
    this.configs.push(config);
    this.saveConfigs();
    this.render();
    
    SmartOffice.log('info', '已添加新配置: ' + config.name);
};

/**
 * @function ConfigListComponent.prototype.updateConfig
 * @description 更新配置
 * @param {Object} updatedConfig - 更新的配置对象
 */
ConfigListComponent.prototype.updateConfig = function(updatedConfig) {
    for (let i = 0; i < this.configs.length; i++) {
        if (this.configs[i].id === updatedConfig.id) {
            this.configs[i] = updatedConfig;
            break;
        }
    }
    
    this.saveConfigs();
    this.render();
    
    SmartOffice.log('info', '已更新配置: ' + updatedConfig.name);
};

/**
 * @function ConfigListComponent.prototype.deleteConfig
 * @description 删除配置
 * @param {string} configId - 配置ID
 */
ConfigListComponent.prototype.deleteConfig = function(configId) {
    const index = this.configs.findIndex(function(config) {
        return config.id === configId;
    });
    
    if (index !== -1) {
        const deletedConfig = this.configs.splice(index, 1)[0];
        this.saveConfigs();
        this.render();
        
        SmartOffice.log('info', '已删除配置: ' + deletedConfig.name);
    }
};

// 注册到全局命名空间
SmartOffice.Components.ConfigList = ConfigListComponent;

SmartOffice.log('info', 'SmartOffice配置列表组件模块初始化完成');
