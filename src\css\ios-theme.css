/**
 * @file iOS主题样式
 * @description 专门的iOS风格组件和交互样式
 * <AUTHOR> Team
 */

/* iOS风格按钮 */
.ios-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-medium);
    font-size: var(--font-size-body);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
    min-height: 44px;
    position: relative;
    overflow: hidden;
}

.ios-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: currentColor;
    opacity: 0;
    transition: opacity var(--animation-fast) ease;
}

.ios-button:active::before {
    opacity: 0.1;
}

/* 按钮变体 */
.ios-button-primary {
    background-color: var(--ios-blue);
    color: white;
}

.ios-button-secondary {
    background-color: var(--text-quaternary);
    color: var(--text-primary);
}

.ios-button-destructive {
    background-color: var(--ios-red);
    color: white;
}

.ios-button-plain {
    background-color: transparent;
    color: var(--ios-blue);
}

.ios-button-plain:active {
    background-color: rgba(0, 122, 255, 0.1);
}

/* iOS风格列表 */
.ios-list {
    background-color: var(--background-secondary);
    border-radius: var(--radius-medium);
    overflow: hidden;
    margin-bottom: var(--spacing-md);
}

.ios-list-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 0.5px solid var(--separator-opaque);
    background-color: var(--background-secondary);
    transition: background-color var(--animation-fast) ease;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
}

.ios-list-item:last-child {
    border-bottom: none;
}

.ios-list-item:active {
    background-color: var(--text-quaternary);
}

.ios-list-item-content {
    flex: 1;
    min-width: 0;
}

.ios-list-item-title {
    font-size: var(--font-size-body);
    color: var(--text-primary);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ios-list-item-subtitle {
    font-size: var(--font-size-footnote);
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ios-list-item-accessory {
    margin-left: var(--spacing-sm);
    color: var(--text-tertiary);
}

.ios-list-item-chevron {
    width: 8px;
    height: 13px;
    fill: var(--text-tertiary);
}

/* iOS风格表单 */
.ios-form-section {
    margin-bottom: var(--spacing-lg);
}

.ios-form-header {
    padding: var(--spacing-sm) var(--spacing-md);
    margin-bottom: var(--spacing-xs);
}

.ios-form-title {
    font-size: var(--font-size-footnote);
    color: var(--text-secondary);
    text-transform: uppercase;
    font-weight: 400;
    letter-spacing: 0.5px;
}

.ios-form-group {
    background-color: var(--background-secondary);
    border-radius: var(--radius-medium);
    overflow: hidden;
}

.ios-form-row {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 0.5px solid var(--separator-opaque);
    min-height: 44px;
}

.ios-form-row:last-child {
    border-bottom: none;
}

.ios-form-label {
    font-size: var(--font-size-body);
    color: var(--text-primary);
    margin-right: var(--spacing-md);
    min-width: 80px;
    flex-shrink: 0;
}

.ios-form-input {
    flex: 1;
    border: none;
    background: none;
    font-size: var(--font-size-body);
    color: var(--text-primary);
    padding: 0;
    outline: none;
    -webkit-appearance: none;
}

.ios-form-input::placeholder {
    color: var(--text-tertiary);
}

/* iOS风格开关 */
.ios-switch {
    position: relative;
    display: inline-block;
    width: 51px;
    height: 31px;
}

.ios-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.ios-switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--text-quaternary);
    transition: var(--animation-normal);
    border-radius: 31px;
}

.ios-switch-slider:before {
    position: absolute;
    content: "";
    height: 27px;
    width: 27px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: var(--animation-normal);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.ios-switch input:checked + .ios-switch-slider {
    background-color: var(--ios-green);
}

.ios-switch input:checked + .ios-switch-slider:before {
    transform: translateX(20px);
}

/* iOS风格分段控制器 */
.ios-segmented-control {
    display: flex;
    background-color: var(--text-quaternary);
    border-radius: var(--radius-small);
    padding: 2px;
    margin-bottom: var(--spacing-md);
}

.ios-segmented-option {
    flex: 1;
    padding: 8px 16px;
    text-align: center;
    font-size: var(--font-size-footnote);
    font-weight: 500;
    color: var(--text-primary);
    background: none;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
}

.ios-segmented-option.active {
    background-color: var(--background-secondary);
    color: var(--text-primary);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* iOS风格提示框 */
.ios-toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-medium);
    font-size: var(--font-size-subhead);
    text-align: center;
    z-index: 10000;
    max-width: 280px;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.toast-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10000;
}

/* iOS风格动作表 */
.ios-action-sheet {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--background-secondary);
    border-radius: var(--radius-medium) var(--radius-medium) 0 0;
    transform: translateY(100%);
    transition: transform var(--animation-normal) cubic-bezier(0.4, 0.0, 0.2, 1);
    z-index: 10000;
    padding-bottom: env(safe-area-inset-bottom, 0px);
}

.ios-action-sheet.show {
    transform: translateY(0);
}

.ios-action-sheet-header {
    padding: var(--spacing-md);
    border-bottom: 0.5px solid var(--separator-opaque);
    text-align: center;
}

.ios-action-sheet-title {
    font-size: var(--font-size-footnote);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.ios-action-sheet-message {
    font-size: var(--font-size-footnote);
    color: var(--text-secondary);
}

.ios-action-sheet-actions {
    padding: 0;
}

.ios-action-sheet-action {
    display: block;
    width: 100%;
    padding: var(--spacing-md);
    border: none;
    background: none;
    font-size: var(--font-size-body);
    color: var(--ios-blue);
    text-align: center;
    cursor: pointer;
    border-bottom: 0.5px solid var(--separator-opaque);
    transition: background-color var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
}

.ios-action-sheet-action:last-child {
    border-bottom: none;
}

.ios-action-sheet-action:active {
    background-color: var(--text-quaternary);
}

.ios-action-sheet-action.destructive {
    color: var(--ios-red);
}

.ios-action-sheet-action.cancel {
    font-weight: 600;
    margin-top: var(--spacing-xs);
    border-top: 0.5px solid var(--separator-opaque);
}

/* iOS风格模态框背景 */
.ios-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 9999;
    opacity: 0;
    transition: opacity var(--animation-normal) ease;
}

.ios-modal-backdrop.show {
    opacity: 1;
}

/* iOS风格徽章 */
.ios-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    padding: 0 6px;
    background-color: var(--ios-red);
    color: white;
    font-size: var(--font-size-caption);
    font-weight: 600;
    border-radius: 10px;
    text-align: center;
    line-height: 1;
}

.ios-badge.secondary {
    background-color: var(--text-secondary);
}

.ios-badge.success {
    background-color: var(--ios-green);
}

/* 触觉反馈增强 */
.haptic-light:active {
    animation: haptic-pulse-light 0.1s ease;
}

.haptic-medium:active {
    animation: haptic-pulse-medium 0.15s ease;
}

.haptic-heavy:active {
    animation: haptic-pulse-heavy 0.2s ease;
}

@keyframes haptic-pulse-light {
    0% { transform: scale(1); }
    50% { transform: scale(0.98); }
    100% { transform: scale(1); }
}

@keyframes haptic-pulse-medium {
    0% { transform: scale(1); }
    50% { transform: scale(0.96); }
    100% { transform: scale(1); }
}

@keyframes haptic-pulse-heavy {
    0% { transform: scale(1); }
    50% { transform: scale(0.94); }
    100% { transform: scale(1); }
}
