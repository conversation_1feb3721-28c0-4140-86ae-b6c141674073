/**
 * @file SmartOffice事件总线系统
 * @description 实现应用内模块间通信的事件机制
 * <AUTHOR> Team
 */

/**
 * @function EventBus
 * @description 事件总线构造函数，实现发布-订阅模式
 * @constructor
 */
function EventBus() {
    /**
     * @property {Object} events - 存储所有事件监听器
     * @private
     */
    this.events = {};
    
    /**
     * @property {number} maxListeners - 单个事件的最大监听器数量
     * @private
     */
    this.maxListeners = 50;
    
    SmartOffice.log('info', 'EventBus实例创建完成');
}

/**
 * @function EventBus.prototype.on
 * @description 注册事件监听器
 * @param {string} eventName - 事件名称
 * @param {Function} callback - 回调函数
 * @param {Object} context - 执行上下文（可选）
 * @returns {EventBus} 返回自身，支持链式调用
 */
EventBus.prototype.on = function(eventName, callback, context) {
    // 参数验证
    if (typeof eventName !== 'string' || !eventName.trim()) {
        SmartOffice.log('error', 'EventBus.on: 事件名称必须是非空字符串');
        return this;
    }
    
    if (typeof callback !== 'function') {
        SmartOffice.log('error', 'EventBus.on: 回调函数必须是function类型');
        return this;
    }
    
    // 初始化事件数组
    if (!this.events[eventName]) {
        this.events[eventName] = [];
    }
    
    // 检查监听器数量限制
    if (this.events[eventName].length >= this.maxListeners) {
        SmartOffice.log('warn', `事件 ${eventName} 的监听器数量已达到上限 ${this.maxListeners}`);
        return this;
    }
    
    // 添加监听器
    this.events[eventName].push({
        callback: callback,
        context: context || null,
        once: false
    });
    
    SmartOffice.log('info', `事件监听器已注册: ${eventName}`);
    return this;
};

/**
 * @function EventBus.prototype.once
 * @description 注册一次性事件监听器
 * @param {string} eventName - 事件名称
 * @param {Function} callback - 回调函数
 * @param {Object} context - 执行上下文（可选）
 * @returns {EventBus} 返回自身，支持链式调用
 */
EventBus.prototype.once = function(eventName, callback, context) {
    if (typeof eventName !== 'string' || !eventName.trim()) {
        SmartOffice.log('error', 'EventBus.once: 事件名称必须是非空字符串');
        return this;
    }
    
    if (typeof callback !== 'function') {
        SmartOffice.log('error', 'EventBus.once: 回调函数必须是function类型');
        return this;
    }
    
    if (!this.events[eventName]) {
        this.events[eventName] = [];
    }
    
    this.events[eventName].push({
        callback: callback,
        context: context || null,
        once: true
    });
    
    SmartOffice.log('info', `一次性事件监听器已注册: ${eventName}`);
    return this;
};

/**
 * @function EventBus.prototype.emit
 * @description 触发事件，执行所有相关监听器
 * @param {string} eventName - 事件名称
 * @param {...*} args - 传递给监听器的参数
 * @returns {EventBus} 返回自身，支持链式调用
 */
EventBus.prototype.emit = function(eventName) {
    if (typeof eventName !== 'string' || !eventName.trim()) {
        SmartOffice.log('error', 'EventBus.emit: 事件名称必须是非空字符串');
        return this;
    }
    
    // 获取传递给监听器的参数
    const args = Array.prototype.slice.call(arguments, 1);
    
    // 检查是否有监听器
    if (!this.events[eventName] || this.events[eventName].length === 0) {
        SmartOffice.log('info', `事件 ${eventName} 没有监听器`);
        return this;
    }
    
    // 复制监听器数组，避免在执行过程中被修改
    const listeners = this.events[eventName].slice();
    const toRemove = [];
    
    // 执行所有监听器
    for (let i = 0; i < listeners.length; i++) {
        const listener = listeners[i];
        
        try {
            // 在指定上下文中执行回调
            if (listener.context) {
                listener.callback.apply(listener.context, args);
            } else {
                listener.callback.apply(null, args);
            }
            
            // 标记一次性监听器待移除
            if (listener.once) {
                toRemove.push(i);
            }
            
        } catch (error) {
            SmartOffice.log('error', `事件 ${eventName} 的监听器执行出错:`, error);
        }
    }
    
    // 移除一次性监听器
    for (let i = toRemove.length - 1; i >= 0; i--) {
        this.events[eventName].splice(toRemove[i], 1);
    }
    
    SmartOffice.log('info', `事件 ${eventName} 已触发，执行了 ${listeners.length} 个监听器`);
    return this;
};

/**
 * @function EventBus.prototype.off
 * @description 移除事件监听器
 * @param {string} eventName - 事件名称
 * @param {Function} callback - 要移除的回调函数（可选）
 * @param {Object} context - 执行上下文（可选）
 * @returns {EventBus} 返回自身，支持链式调用
 */
EventBus.prototype.off = function(eventName, callback, context) {
    if (typeof eventName !== 'string' || !eventName.trim()) {
        SmartOffice.log('error', 'EventBus.off: 事件名称必须是非空字符串');
        return this;
    }
    
    // 如果事件不存在，直接返回
    if (!this.events[eventName]) {
        return this;
    }
    
    // 如果没有指定回调函数，移除所有监听器
    if (!callback) {
        delete this.events[eventName];
        SmartOffice.log('info', `事件 ${eventName} 的所有监听器已移除`);
        return this;
    }
    
    // 移除指定的监听器
    const listeners = this.events[eventName];
    for (let i = listeners.length - 1; i >= 0; i--) {
        const listener = listeners[i];
        
        if (listener.callback === callback && 
            (!context || listener.context === context)) {
            listeners.splice(i, 1);
            SmartOffice.log('info', `事件 ${eventName} 的指定监听器已移除`);
        }
    }
    
    // 如果没有监听器了，删除事件
    if (listeners.length === 0) {
        delete this.events[eventName];
    }
    
    return this;
};

/**
 * @function EventBus.prototype.clear
 * @description 清除所有事件监听器
 * @returns {EventBus} 返回自身，支持链式调用
 */
EventBus.prototype.clear = function() {
    this.events = {};
    SmartOffice.log('info', '所有事件监听器已清除');
    return this;
};

/**
 * @function EventBus.prototype.getListeners
 * @description 获取指定事件的监听器数量
 * @param {string} eventName - 事件名称
 * @returns {number} 监听器数量
 */
EventBus.prototype.getListeners = function(eventName) {
    if (!this.events[eventName]) {
        return 0;
    }
    return this.events[eventName].length;
};

/**
 * @function EventBus.prototype.hasListeners
 * @description 检查指定事件是否有监听器
 * @param {string} eventName - 事件名称
 * @returns {boolean} 是否有监听器
 */
EventBus.prototype.hasListeners = function(eventName) {
    return this.getListeners(eventName) > 0;
};

// 创建全局事件总线实例
SmartOffice.Core.EventBus = new EventBus();

// 定义应用级别的标准事件名称
SmartOffice.Events = {
    // 应用生命周期事件
    APP_INIT: 'app:init',
    APP_READY: 'app:ready',
    APP_ERROR: 'app:error',
    
    // 页面导航事件
    PAGE_CHANGE: 'page:change',
    PAGE_BEFORE_CHANGE: 'page:beforeChange',
    PAGE_AFTER_CHANGE: 'page:afterChange',
    
    // 配置管理事件
    CONFIG_CREATE: 'config:create',
    CONFIG_UPDATE: 'config:update',
    CONFIG_DELETE: 'config:delete',
    CONFIG_SELECT: 'config:select',
    
    // 文件处理事件
    FILE_UPLOAD_START: 'file:uploadStart',
    FILE_UPLOAD_PROGRESS: 'file:uploadProgress',
    FILE_UPLOAD_SUCCESS: 'file:uploadSuccess',
    FILE_UPLOAD_ERROR: 'file:uploadError',
    
    // 数据处理事件
    DATA_PARSE_START: 'data:parseStart',
    DATA_PARSE_SUCCESS: 'data:parseSuccess',
    DATA_PARSE_ERROR: 'data:parseError',
    
    // UI交互事件
    UI_LOADING_SHOW: 'ui:loadingShow',
    UI_LOADING_HIDE: 'ui:loadingHide',
    UI_TOAST_SHOW: 'ui:toastShow',
    UI_HAPTIC_FEEDBACK: 'ui:hapticFeedback'
};

// 冻结事件名称对象，防止意外修改
Object.freeze(SmartOffice.Events);

SmartOffice.log('info', 'SmartOffice事件系统初始化完成');
