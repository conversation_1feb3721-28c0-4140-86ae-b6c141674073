# Product Context: GoMyHire 移动端快速透视分析

## 1. 项目存在的原因 (Why)
GoMyHire 的运营管理人员（如区域经理、市场分析师）经常需要在移动场景下快速获取基于最新数据的洞察，以便及时做出决策。传统的数据分析工具往往依赖桌面环境或复杂的后端系统，不适合移动端即时分析的需求。本项目旨在填补这一空白，提供一个轻量级、高效、纯前端的移动端数据透视解决方案。

## 2. 项目解决的问题 (Problems Solved)
- **移动端数据分析不便**: 现有工具在移动设备上体验不佳或功能受限。
- **数据更新滞后**: 依赖人工处理或后端批处理，导致分析结果无法实时反映最新业务状况。
- **重复性分析工作**: 用户需要为相同的分析视角（行列筛选组合）反复手动配置透视表。
- **数据隐私顾虑**: 将敏感业务数据上传到第三方服务器存在安全风险。
- **性能瓶颈**: 在浏览器端处理较大数据文件（如5MB）时可能出现的卡顿或崩溃。

## 3. 项目如何工作 (How it Should Work)
### 3.1 核心流程
1.  **一次性配置 (桌面或移动端)**:
    *   用户通过“透视分析配置”模块创建并保存一个或多个透视表配置。
    *   每个配置包含：
        *   配置名称（例如：“按城市统计GMV”）
        *   数据源（默认为最近上传的文件）
        *   行字段
        *   列字段
        *   值字段（及聚合方式，如求和、平均）
        *   筛选条件
    *   配置保存在浏览器本地存储 (IndexedDB)。

2.  **日常使用 (移动端)**:
    *   用户打开应用，进入主界面（仪表盘）。
    *   主界面展示一个“上传数据”按钮和已保存的透视表配置列表（或其对应的空仪表盘小部件）。
    *   用户点击“上传数据”，选择本地表格文件 (CSV, XLSX, 最大5MB)。
    *   应用在后台（Web Worker）解析文件，验证数据结构（基于第一个预设配置的字段要求或通用规则）。
    *   验证通过后，应用根据所有预设的透视表配置，自动计算并更新对应的透视表结果。
    *   结果实时展现在仪表盘的各个小部件中。

### 3.2 用户体验目标
- **简洁直观**: 移动端界面清晰易懂，操作步骤最少。
- **快速响应**: 文件上传和透视表生成过程迅速，用户等待时间短。
- **自动化**: 最大限度减少用户手动操作，上传文件后自动完成分析。
- **可定制化**: 用户可以灵活定义和管理自己关心的分析视角。
- **数据安全**: 所有数据处理均在用户本地浏览器完成，不上传至服务器。
- **离线可用**: （可选特性）在无网络情况下，仍可查看基于已缓存数据的分析结果。

## 4. 关键成功指标 (Key Success Metrics)
- **首次透视表加载时间**: 从文件上传完成到第一个透视表显示结果的时间（目标：5MB文件 < 10秒）。
- **多透视表更新时间**: 从文件上传完成到所有预设透视表（假设5个）更新完成的时间（目标：5MB文件 < 20秒）。
- **用户配置便捷性**: 用户完成一个新的透视表配置的平均操作步骤（目标：< 5步）。
- **移动端用户满意度**: 通过用户调研或反馈收集。
- **最大可处理文件大小**: 确保在主流移动设备上流畅处理5MB文件的能力。
